import type { ChoiceItem } from "../field/types";

export interface ItemQuery extends PageQuery {
  projectId?: string | number;
  itemId?: number;
  keyword?: string;
  stateIds?: number[];
  ownerIds?: number[];
  sortFieldId?: number;
  fieldIds?: number[];
}

export interface ListItemVo extends BaseEntity {
  itemId: number;
  id: string | number;
  displayId: string;
  itemTitle: string;
  textColor?: string;
  textBold?: number;
  responseElapsed?: number;
  resolveElapsed?: number;
  values: ListItemFieldVo[];
}

export interface ListItemFieldVo {
  id: number;
  choiceId?: number | number[];
  value: string;
}

export interface stateVO {
  stateId: number;
  stateName: string;
  stateOptionId: number;
  stateDescription: string;
  stateColor: string;
}

export interface ProjectMemberVo {
  userId: number;
  userName: string;
  nickName: string;
  userType: number;
  userTypeName: string;
  accounTypeId: number;
  accountTypeName: string;
}

export interface ItemHistoryVO {
  historyList: HistoryInfoVO[];
  changelogList: ChangelogInfoVO[];
}

 export interface HistoryInfoVO {
  seqNo: number;
  userId: number;
  userName: string;
  stateFrom: number;
  stateFromName: string;
  stateTo: number;
  stateToName: string;
  transition: number;
  transitionName: string;
  ownerFrom: number;
  ownerFromName: string;
  ownerTo: number;
  ownerToName: string;
  logTime: string;
}

export interface ChangelogInfoVO {
  changelogId: number;
  logTime: string;
  changedById: number;
  changedByUserName: string;
  changedByUserAvatarUrl: string;
  changelogField: ChangelogFieldVO[];
}

export interface ChangelogFieldVO {
  fieldId: number;
  fieldName: string;
  changelogId: number;
  changeFrom: string;
  changeTo: string;
  changeFromHtml: string;
  changeToHtml: string;
  changeFromPlainText: string;
  changeToPlainText: string;
  description: string;
}

export interface ItemVO extends BaseEntity {
  id: string | number;
  projectId: number;
  itemId: number;
  displayId: string;
  itemTitle: string;
  moduleId: number;
  typeId: number;
  ownerId: number;
  ownerName: string | '';
  stateId: number;
  stateName: string | '';
  createdTime: string;
  createdBy: string | '';
  modifiedTime: string;
  modifiedBy: string | '';
  assignedTime: string;
  fields: ItemFieldVO[];
  nextWorkflowTransitionState: WorkflowTransitionStateVo;
  stateAttributes?: workflowStateAttributes;
}

export interface ItemFieldVO {
  fieldId: number;
  value: string;
  choiceId?: number | number[];
  additionInfo?: any[];
}

export interface WorkflowTransitionStateVo {
  fromStateId: number;
  fromStateName: string;
  nextStates: WorkflowTransitionNextStateVo[];
  stateId: number;
  stateName: string;
  stateOptionId: number;
  level: number;
}

export interface WorkflowTransitionNextStateVo {
  transitionId: number;
  transitionName: string;
  toStateId: number;
  toStateName: string;
  stateOptionId: number;
  disabled?: boolean;
  mandatoryFieldIds?: number[];
  stateAttributes?: workflowStateAttributes;
}

export interface workflowStateAttributes {
  applicableOwners?: ChoiceItem[];
  readonlyFieldIds?: number[];
  invisibleFieldIds?: number[];
}
