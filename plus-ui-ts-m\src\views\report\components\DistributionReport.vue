<template>
  <div class="page-container">
    <van-nav-bar title="分布图" fixed left-arrow @click-left="$router.back()"/>
    <div class="report-content">
      <div ref="chartContainer" class="chart-container"></div>
    </div>

    <!-- 底部导航栏 -->
    <van-tabbar route fixed>
      <van-tabbar-item to="/home" icon="home-o">首页</van-tabbar-item>
      <van-tabbar-item to="/projects" icon="apps-o">项目</van-tabbar-item>
      <van-tabbar-item to="/reports" icon="chart-trending-o">报表</van-tabbar-item>
      <van-tabbar-item to="/profile" icon="user-o">我的</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

const chartContainer = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

// 示例数据
const distributionData = [
  { name: '硬件问题', value: 35 },
  { name: '软件问题', value: 28 },
  { name: '网络问题', value: 20 },
  { name: '配置问题', value: 12 },
  { name: '其他问题', value: 5 }
]

const initChart = () => {
  if (chartContainer.value) {
    // 销毁之前的实例
    if (chartInstance) {
      chartInstance.dispose()
    }
    
    // 初始化新的实例
    chartInstance = echarts.init(chartContainer.value)
    
    // 配置图表选项
    const option = {
      title: {
        text: '问题类型分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b} : {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '问题类型',
          type: 'pie',
          radius: '50%',
          data: distributionData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    
    // 设置配置项
    chartInstance.setOption(option)
  }
}

const refreshData = () => {
  console.log('刷新数据')
  // 这里可以重新获取数据并更新图表
  if (chartInstance) {
    chartInstance.setOption({
      series: [{
        data: distributionData
      }]
    })
  }
}

onMounted(() => {
  console.log('分布图报表页面已加载')
  initChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  })
})

onBeforeUnmount(() => {
  // 销毁图表实例
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})
</script>

<style scoped>
.page-container {
    position: relative;
    min-height: 100vh;
    padding-top: 46px;
    padding-bottom: 50px;
    box-sizing: border-box;
  }

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px 0;
  border-bottom: 1px solid #e4e7ed;
}

.report-content {
  background-color: white;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.chart-container {
  width: 100%;
  height: 300px;
}
</style>