<template>
    <div class="page-container">
      <!-- 顶部栏 -->
      <van-nav-bar title="工单列表" fixed left-arrow @click-left="$router.back()"/>
  

  
      <!-- 工单列表 -->
      <ItemList :project-id="projectId" />
  
      <!-- 悬浮新建按钮 -->
      <van-button type="primary" icon="plus" class="fab-btn" @click="$router.push('/new')"></van-button>
  
      <!-- 底部导航栏 -->
      <van-tabbar route fixed>
        <van-tabbar-item to="/home" icon="home-o">首页</van-tabbar-item>
        <van-tabbar-item to="/projects" icon="apps-o">项目</van-tabbar-item>
        <van-tabbar-item to="/reports" icon="chart-trending-o">报表</van-tabbar-item>
        <van-tabbar-item to="/profile" icon="user-o">我的</van-tabbar-item>
      </van-tabbar>
    </div>
  
    
  </template>
  
  <script setup lang="ts">
  import { ref, computed } from 'vue'
  import { useRoute } from 'vue-router'
  import ItemList from '@/components/ItemList.vue'
    
  const route = useRoute()
  const projectId = computed(() => route.query.projectId as string || '1')

  </script>
  
  <style scoped>
  .page-container {
    position: relative;
    min-height: 100vh;
    padding-top: 46px;
    padding-bottom: 50px;
    box-sizing: border-box;
  }

  .fab-btn {
    position: fixed;
    bottom: 70px;
    right: 16px;
    z-index: 999;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    opacity: 0.9;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
  }
  
  </style>