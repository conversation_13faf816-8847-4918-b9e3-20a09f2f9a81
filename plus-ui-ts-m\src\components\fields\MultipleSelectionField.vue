<template>
  <div 
    class="normal-value editable-field-container field-value-left consistent-height"
    @click="editField"
  >
    <div v-if="displayValues && displayValues.length > 0" class="tags-container">
      <span v-for="tag in displayValues" :key="tag" class="tag">{{ tag }}</span>
    </div>
    <span v-else-if="displayValue" class="normal-value">{{ displayValue }}</span>
    <span v-else class="empty-value">空</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { PageFieldVo } from '@/api/field/types'

interface Props {
  field: PageFieldVo
  modelValue: string[] | string | number
  choices: Array<{choiceId: number, choiceName: string}> | undefined
}

const props = defineProps<Props>()

const emit = defineEmits<{ 
  (e: 'edit', field: PageFieldVo, event: Event): void
}>()

const displayValues = computed(() => {
  // 处理多选情况
  if (Array.isArray(props.modelValue) && props.choices) {
    return props.modelValue.map(choiceId => {
      const choice = props.choices!.find(c => c.choiceId === choiceId)
      return choice ? choice.choiceName : choiceId
    })
  }
  return []
})

const displayValue = computed(() => {
  // 处理单选情况
  if (!Array.isArray(props.modelValue) && props.modelValue) {
    if (props.choices) {
      const choice = props.choices.find(c => c.choiceId === props.modelValue)
      return choice ? choice.choiceName : props.modelValue
    }
    return props.modelValue
  }
  return ''
})

const editField = (event: Event) => {
  emit('edit', props.field, event)
}
</script>

<style scoped>
.editable-field-container {
  width: 100%;
  padding: 6px 0;
  line-height: 1.5;
  box-sizing: border-box;
  text-align: left;
}

.normal-value {
  color: #333;
  font-size: 14px;
  text-align: left;
}

.empty-value {
  color: #ccc;
  font-size: 14px;
  text-align: left;
}

.consistent-height {
  min-height: 32px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag {
  background-color: #f0f2f5;
  color: #666;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}
</style>