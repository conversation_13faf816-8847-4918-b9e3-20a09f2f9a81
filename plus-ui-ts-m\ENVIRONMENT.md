# 环境配置说明

本项目支持多环境配置，通过不同的环境变量文件来管理不同环境的配置。

## 环境文件

### `.env.development` - 开发环境
- 用于本地开发
- 启用调试模式
- 使用本地 API 地址
- 生成 sourcemap 便于调试

### `.env.production` - 生产环境
- 用于生产环境部署
- 禁用调试模式
- 使用生产 API 地址
- 启用代码压缩和优化

## 构建命令

```bash
# 开发环境
npm run dev

# 生产环境构建
npm run build:prod

# 预览生产构建
npm run preview:prod

# 分析构建包大小
npm run analyze
```

## 环境变量说明

### 基础配置
- `VITE_APP_TITLE`: 应用标题
- `VITE_API_BASE_URL`: API 基础地址
- `VITE_APP_BASE_API`: API 基础路径
- `VITE_APP_PORT`: 应用端口号

### 调试和监控
- `VITE_DEBUG`: 是否启用调试模式
- `VITE_ENABLE_PERFORMANCE_MONITORING`: 是否启用性能监控
- `VITE_ENABLE_ERROR_REPORTING`: 是否启用错误上报
- `VITE_ENABLE_USER_TRACKING`: 是否启用用户行为追踪

### 构建优化
- `VITE_BUILD_OUTPUT_DIR`: 构建输出目录
- `VITE_GENERATE_SOURCEMAP`: 是否生成 sourcemap
- `VITE_ENABLE_MINIFICATION`: 是否启用代码压缩
- `VITE_ENABLE_JS_CODE_SPLITTING`: 是否启用 JS 代码分割
- `VITE_ENABLE_CSS_CODE_SPLITTING`: 是否启用 CSS 代码分割
- `VITE_ENABLE_TREE_SHAKING`: 是否启用 tree shaking
- `VITE_MAX_CHUNK_SIZE`: 最大 chunk 大小（KB）

### 压缩和优化
- `VITE_ENABLE_GZIP`: 是否启用 gzip 压缩
- `VITE_ENABLE_BROTLI`: 是否启用 brotli 压缩
- `VITE_ENABLE_IMAGE_COMPRESSION`: 是否启用图片压缩

### 预加载和预取
- `VITE_ENABLE_PRELOAD`: 是否启用预加载
- `VITE_ENABLE_PREFETCH`: 是否启用预取

### 安全配置
- `VITE_APP_ENCRYPT`: 是否启用接口加密
- `VITE_APP_RSA_PUBLIC_KEY`: RSA 公钥（用于加密传输）
- `VITE_APP_RSA_PRIVATE_KEY`: RSA 私钥（用于解密响应）
- `VITE_APP_CLIENT_ID`: 客户端 ID

### 通信配置
- `VITE_APP_WEBSOCKET`: 是否启用 WebSocket（默认使用 SSE 推送）

## 自定义环境

如果需要添加新的环境（如测试环境），可以创建 `.env.staging` 文件：

```bash
# 创建测试环境配置
cp .env.production .env.staging
# 然后修改相应的配置
```

## 注意事项

1. **NODE_ENV**: 不要在 `.env` 文件中设置 `NODE_ENV`，Vite 会自动根据构建模式设置
2. **环境变量前缀**: 所有客户端可用的环境变量必须以 `VITE_` 开头
3. **类型安全**: 在 `env.d.ts` 中定义了所有环境变量的类型
4. **构建优化**: 生产环境会自动启用代码压缩、tree shaking 等优化

## 部署建议

1. **CDN 配置**: 如果有 CDN，可以设置 `VITE_CDN_BASE_URL`
2. **API 地址**: 确保生产环境的 API 地址正确配置
3. **监控配置**: 根据需要在生产环境启用性能监控和错误上报
4. **压缩配置**: 生产环境建议启用 gzip 和 brotli 压缩 