<template>
  <div 
    class="normal-value editable-field-container field-value-left consistent-height"
    @click="editField"
  >
    <span v-if="displayValue" class="normal-value">{{ displayValue }}</span>
    <span v-else class="empty-value">空</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { PageFieldVo } from '@/api/field/types'

interface Props {
  field: PageFieldVo
  modelValue: string
}

const props = defineProps<Props>()

const emit = defineEmits<{ 
  (e: 'edit', field: PageFieldVo, event: Event): void
}>()

const displayValue = computed(() => {
  // 这里可以添加base64解码逻辑
  return props.modelValue || ''
})

const editField = (event: Event) => {
  emit('edit', props.field, event)
}
</script>

<style scoped>
.editable-field-container {
  width: 100%;
  padding: 6px 0;
  line-height: 1.5;
  box-sizing: border-box;
  text-align: left;
}

.normal-value {
  color: #333;
  font-size: 14px;
  text-align: left;
}

.empty-value {
  color: #ccc;
  font-size: 14px;
  text-align: left;
}

.consistent-height {
  min-height: 32px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
</style>