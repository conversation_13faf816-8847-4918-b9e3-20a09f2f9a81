<template>
  <div class="service-catalog-page">
    <van-nav-bar title="服务目录" fixed />
    
    <div class="page-container">
      <div class="content">
        <!-- 搜索栏 -->
        <van-search
          v-model="searchValue"
          placeholder="搜索服务"
          @search="onSearch"
        />
        
        <!-- 服务分类 -->
        <van-tabs v-model="activeCategory" sticky>
          <van-tab title="全部" name="all">
            <service-list :category="'all'" :search="searchValue" />
          </van-tab>
          <van-tab title="IT设备" name="device">
            <service-list :category="'device'" :search="searchValue" />
          </van-tab>
          <van-tab title="软件服务" name="software">
            <service-list :category="'software'" :search="searchValue" />
          </van-tab>
          <van-tab title="网络服务" name="network">
            <service-list :category="'network'" :search="searchValue" />
          </van-tab>
        </van-tabs>
      </div>
    </div>

    <!-- 底部导航 -->
    <van-tabbar v-model="activeTab" @change="onTabChange">
      <van-tabbar-item icon="home-o" to="/dashboard">首页</van-tabbar-item>
      <van-tabbar-item icon="orders-o" to="/work-order">工单</van-tabbar-item>
      <van-tabbar-item icon="apps-o" to="/service-catalog">服务</van-tabbar-item>
      <van-tabbar-item icon="bookmark-o" to="/knowledge">知识</van-tabbar-item>
      <van-tabbar-item icon="user-o" to="/profile">我的</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ServiceList from '@/components/ServiceList.vue'

const activeCategory = ref('all')
const activeTab = ref(2)
const searchValue = ref('')

const onSearch = (value: string) => {
  console.log('搜索:', value)
}

const onTabChange = (index: number) => {
  activeTab.value = index
}
</script>

<style lang="scss" scoped>
.service-catalog-page {
  padding-top: 46px;
  padding-bottom: 50px;
}
</style> 