<template>
  <div class="knowledge-page">
    <van-nav-bar title="知识库" fixed left-arrow @click-left="$router.back()"/>
    
    <div class="page-container">
      <div class="content">
        <!-- 搜索栏 -->
        <van-search
          v-model="searchValue"
          placeholder="搜索知识文章"
          @search="onSearch"
        />
        
        <!-- 知识分类 -->
        <van-tabs v-model="activeCategory" sticky>
          <van-tab title="全部" name="all">
            <knowledge-list :category="'all'" :search="searchValue" />
          </van-tab>
          <van-tab title="常见问题" name="faq">
            <knowledge-list :category="'faq'" :search="searchValue" />
          </van-tab>
          <van-tab title="操作指南" name="guide">
            <knowledge-list :category="'guide'" :search="searchValue" />
          </van-tab>
          <van-tab title="故障排除" name="troubleshoot">
            <knowledge-list :category="'troubleshoot'" :search="searchValue" />
          </van-tab>
        </van-tabs>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <van-tabbar route fixed>
      <van-tabbar-item to="/home" icon="home-o">首页</van-tabbar-item>
      <van-tabbar-item to="/projects" icon="apps-o">项目</van-tabbar-item>
      <van-tabbar-item to="/reports" icon="chart-trending-o">报表</van-tabbar-item>
      <van-tabbar-item to="/profile" icon="user-o">我的</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import KnowledgeList from '@/components/KnowledgeList.vue'

const activeCategory = ref('all')
const activeTab = ref(3)
const searchValue = ref('')

const onSearch = (value: string) => {
  console.log('搜索:', value)
}

const onTabChange = (index: number) => {
  activeTab.value = index
}
</script>

<style lang="scss" scoped>
.knowledge-page {
  padding-top: 46px;
  padding-bottom: 50px;
}
</style> 