<template>
    <div class="app-container">
      <!-- 顶部状态栏 -->
      <div class="status-bar">
        <h1 class="status-title">ITSM项目</h1>
      </div>
  
      <!-- 内容区域 -->
      <div class="content">
        <div v-if="loading" class="loading">
          <van-loading type="spinner" color="#409EFF" />
          <p>加载中...</p>
        </div>
        <div v-else-if="error" class="error">
          <p>{{ error }}</p>
          <van-button type="primary" @click="fetchprojects">重试</van-button>
        </div>
        <ProjectList v-else :projects="projects" />
      </div>
  
      <!-- 底部导航栏 -->
      <van-tabbar route fixed>
        <van-tabbar-item to="/home" icon="home-o">首页</van-tabbar-item>
        <van-tabbar-item to="/projects" icon="apps-o">项目</van-tabbar-item>
        <van-tabbar-item to="/reports" icon="chart-trending-o">报表</van-tabbar-item>
        <van-tabbar-item to="/profile" icon="user-o">我的</van-tabbar-item>
      </van-tabbar>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, onMounted } from 'vue'
  import projectList from '@/components/ProjectList.vue'
  import { getProjectListByUser } from '@/api/general/index'
  import type { ProjectListData } from '@/api/general/types'
  
  const projects = ref<ProjectListData[]>([])
  const loading = ref(true)
  const error = ref('')
  
  const fetchprojects = async () => {
    try {
      loading.value = true
      error.value = ''
      const response = await getProjectListByUser()
      
      // 获取children数据作为项目数据
      let allChildren: ProjectListData[] = []
      
      if (Array.isArray(response.data)) {
        // 如果response.data是数组，遍历数组中的每个对象，收集所有children
        response.data.forEach(item => {
          if (item.children && Array.isArray(item.children)) {
            allChildren = allChildren.concat(item.children)
          }
        })
      } else if (response.data && Array.isArray(response.data.children)) {
        // 如果response.data是对象且包含children数组
        allChildren = response.data.children
      }
      
      projects.value = allChildren
    } catch (err) {
      console.error('获取项目数据失败:', err)
      error.value = '获取项目数据失败，请重试'
    } finally {
      loading.value = false
    }
  }
  
  onMounted(() => {
    fetchprojects()
  })
  </script>
  
  <style scoped>
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
    -webkit-tap-highlight-color: transparent;
  }
  
  .app-container {
    width: 100%;
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background-color: white;
  }
  
  .status-bar {
    height: 44px;
    width: 100%;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    position: relative;
    z-index: 10;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }
  
  .status-title {
    font-size: 16px;
    font-weight: bold;
  }
  
  .content {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding: 15px;
    background-color: #f5f7fa;
  }
  
  .loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #999;
  }
  
  .loading p {
    margin-top: 10px;
    font-size: 14px;
  }
  
  .error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #F56C6C;
  }
  
  .error p {
    margin-bottom: 15px;
    font-size: 14px;
  }
  </style>