<template>
  <FieldPopupWrapper
    v-model:show="show"
    :title="field?.fieldName || '下拉选择'"
    position="bottom"
    round
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <van-radio-group v-model="localValue" class="radio-group">
      <van-cell-group>
        <van-cell 
          v-for="choice in choices" 
          :key="choice.choiceId" 
          clickable 
          @click="localValue = choice.choiceId"
        >
          <template #title>
            <van-radio :name="choice.choiceId">{{ choice.choiceName }}</van-radio>
          </template>
        </van-cell>
      </van-cell-group>
    </van-radio-group>
  </FieldPopupWrapper>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import type { PageFieldVo } from '@/api/field/types'
import FieldPopupWrapper from './FieldPopupWrapper.vue'

interface Props {
  show: boolean
  field: PageFieldVo
  modelValue: string | number
  choices: Array<{choiceId: number, choiceName: string}> | undefined
}

const props = defineProps<Props>()

const emit = defineEmits<{ 
  (e: 'update:show', value: boolean): void,
  (e: 'update:modelValue', value: string | number): void,
  (e: 'confirm', value: string | number): void,
  (e: 'cancel'): void
}>()

const localValue = ref(props.modelValue)

const show = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

watch(() => props.show, (newVal) => {
  if (newVal) {
    localValue.value = props.modelValue
  }
})

const handleConfirm = () => {
  // 先更新modelValue，再触发confirm事件
  emit('update:modelValue', localValue.value)
  emit('confirm', localValue.value)
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.radio-group {
  margin-top: 16px;
}

.radio-group :deep(.van-radio) {
  flex: 1;
}

.radio-group :deep(.van-cell) {
  padding: 10px 16px;
}
</style>