import { to } from 'await-to-js';
import { getToken, removeToken, setToken } from '@/utils/auth';
import { login as loginApi, logout as logoutApi, getInfo as getUserInfo } from '@/api/login';
import type { LoginData } from '@/api/types';
// import { ProjectAccountTypePermission, ProjectListData } from '@/api/servicetrack/project/types';
// import { getProjectListByUser } from '@/api/servicetrack/project';
import defAva from '@/assets/images/profile.jpg';
import store from '@/stores';

export const useUserStore = defineStore('user', () => {
  const token = ref(getToken());
  const name = ref('');
  const nickname = ref('');
  const userId = ref<string | number>('');
  const tenantId = ref<string>('');
  const avatar = ref('');
  const roles = ref<Array<string>>([]); // 用户角色编码集合 → 判断路由权限
  const permissions = ref<Array<string>>([]); // 用户权限编码集合 → 判断按钮权限
  const stUserType = ref<number>(0);
  const stUserId = ref<number>(0);
  const stAdmin = ref<number>(0);
  const deptName = ref<string>('');
  const projectList = ref<any[]>([]);
  // const workProjectList => //ref<ProjectListData[]>([]);
  const workProjectList =ref<any[]>([]);
  //ref<ProjectListData[]>([]);
  const baseProject = ref<any| null>(null);// ref<ProjectListData | null>(null);
  /**
   * 登录
   * @param userInfo
   * @returns
   */
  const login = async (userInfo: LoginData): Promise<void> => {
    const [err, res] = await to(loginApi(userInfo));
    if (res) {
      const data = res.data;
      setToken(data.access_token || '');
      token.value = data.access_token || '';
      stAdmin.value = data.stAdmin || 0;
      stUserType.value = data.stUserType || 0;
      return Promise.resolve();
    }
    return Promise.reject(err);
  };

  // 获取用户信息
  const getInfo = async (): Promise<void> => {
    const [err, res] = await to(getUserInfo());
    if (res) {
      const resdata = res.data;
      const user = resdata.user ;
      const profile = user && (user.avatar == '' || user.avatar == null) ? defAva : (user ? user.avatar : defAva);

      if (resdata.roles && resdata.roles.length > 0) {
        // 验证返回的roles是否是一个非空数组
        roles.value = resdata.roles;
        permissions.value = resdata.permissions || [];
      } else {
        roles.value = ['ROLE_DEFAULT'];
      }
      if (user) {
        name.value = user.userName;
        nickname.value = user.nickName;
        userId.value = user.userId;
        stUserId.value = user.externalUserId;
        tenantId.value = user.tenantId;
        stUserType.value = user.stUserType || 0;
        stAdmin.value = user.stAdmin || 0;
        deptName.value = user.deptName;
      }
      avatar.value = profile;
      // userId.value = user.userId;
      // stUserId.value = user.externalUserId;
      // tenantId.value = user.tenantId;
      // stUserType.value = user.stUserType | 0;
      // stAdmin.value = user.stAdmin | 0;
      // if ((!projectList.value || !baseProject.value)) {-- to do
      //   // 获取项目列表
      //   const {data} = await getProjectListByUser();
      //   if (data) {
      //     // 如果项目列表长度大于1，则设置基础项目 --to do, need to get base project id by user setting once user login to select one base project
      //     if (data.length > 1) {
      //     }

      //     setProjectList(data, 0);
      //   }
      // }
      return Promise.resolve();
    }
    return Promise.reject(err);
  };

  // 注销
  const logout = async (): Promise<void> => {
    await logoutApi();
    token.value = '';
    roles.value = [];
    permissions.value = [];
    removeToken();
  };

  const setAvatar = (value: string) => {
    avatar.value = value;
  };  
  // const setProjectList = (value: ProjectListData[], baseProjectId: number) => {
  //   projectList.value = value;
  //   if (projectList.value.length > 1) {
  //     if (baseProjectId == 0) {
  //       baseProject.value = projectList.value[0];
  //     } else {
  //       baseProject.value = projectList.value.find((item) => item.projectId == baseProjectId) || null;
  //     }
  //   } else if (projectList.value.length == 1) {
  //     baseProject.value = projectList.value[0];
  //   }
  //   if (baseProject.value) {
  //     workProjectList.value = baseProject.value.children || [];
  //   }
  // };
  // const getAccountTypePermission = (projectId: number): ProjectAccountTypePermission | null => {
  //   let accountTypePermission: ProjectAccountTypePermission = {
  //     canNewIncident: false,
  //     canEditIncident: false,
  //     canDeleteIncident: false,
  //     canUploadAttachment: false,
  //     canDeleteAttachment: false
  //   };
  //   const matchedWorkProject = workProjectList.value.find((item) => item.projectId == projectId);
  //   if (matchedWorkProject) {
  //     accountTypePermission = matchedWorkProject.accountTypePermission;
  //   }
  //   return accountTypePermission;
  // };
  return {
    userId,
    stUserId,
    tenantId,
    token,
    nickname,
    avatar,
    roles,
    permissions,
    workProjectList,
    projectList,
    baseProject,
    stUserType,
    stAdmin,
    deptName,
    login,
    getInfo,
    logout,
    setAvatar,
  //  setProjectList,
  //  getAccountTypePermission
  };
});

export default useUserStore;
// 非setup
export function useUserStoreHook() {
  return useUserStore(store);
}
