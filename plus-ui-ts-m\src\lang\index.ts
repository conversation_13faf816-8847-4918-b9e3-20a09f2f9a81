import { createI18n } from 'vue-i18n';
import { LanguageEnum } from '@/enums/LanguageEnum';
import { useStorage } from '@vueuse/core';

// 定义语言包模块类型
interface LocaleModule {
  default: Record<string, any>;
}

// 动态加载语言包
const loadLocaleMessages = async () => {
  const locales = import.meta.glob<LocaleModule>('./**/*.js');
  const messages: Record<string, any> = {};

  for (const path in locales) {
    const matched = path.match(/([A-Za-z0-9-_]+)\/child\/([A-Za-z0-9-_]+)\.js/i);
    if (matched && matched.length > 2) {
      let locale = matched[1];
      const moduleName = matched[2];
      //match language enum and locale folder language name
      if( locale.indexOf("en") > -1){
        locale = LanguageEnum.en_US;;
      }
      else if( locale.indexOf("zh") > -1){
        locale = LanguageEnum.zh_CN;
      }

      const module = await locales[path]();
      if (!messages[locale]) {
        messages[locale] = {};
      }
      messages[locale][moduleName] = module.default;
    }
  }
  return messages;
};

// 获取当前语言
export const getLanguage = (): LanguageEnum => {
  const language = useStorage<LanguageEnum>('language', LanguageEnum.zh_CN);
  if (language.value) {
    return language.value;
  }
  return LanguageEnum.zh_CN;
};

// 创建i18n实例
const i18n = createI18n({
  globalInjection: true,
  allowComposition: true,
  legacy: false,
  locale: getLanguage(),
  messages: await loadLocaleMessages()
});

export default i18n;
