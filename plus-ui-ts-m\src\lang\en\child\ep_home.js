export default {
  knowledge_base_query: 'Knowledge base query',
  enter_keyword: 'Input Keyword',
  search_knowledge: 'Search',
  event_list: 'Event list',
  inprogress: 'In progress',
  completed: 'Completed',
  dialogue_update: 'Dialogue update',
  event_id: 'Event ID',
  title: 'Title',
  age: 'Age',
  three_level_classification: 'Three-level classification',
  date_of_submission: 'Date of submission',
  predicted_response: 'Predicted response',
  anticipated_settlement: 'Anticipated settlement',
  current_state: 'Current state',
  assigner: 'Assigner',

  new_request: 'New request',
  install_printer: 'Install printer',
  install_printer_desc: 'Need to install or connect printer',
  oa_system: 'OA system',
  oa_system_desc: 'OA system usage issues and account/permission adjustment',
  phone_failure: 'Phone failure',
  phone_failure_desc: 'Phone answering and configuration failure',
  sap_system: 'SAP system',
  sap_system_desc: 'SAP system usage issues and account/permission adjustment',
  email_failure: 'Email sending or receiving failure',
  email_failure_desc: 'Client unable to receive or send email',
  nailing: 'Nailing',
  nailing_desc:
    'Nailing system usage issues, account and permission adjustments',
  network_failure: 'Network failure',
  network_failure_desc:
    'Client network connection abnormality, unable to connect to the network',

  announcement: 'Announcement',
  emergency_situation:
    'If you have an emergency, you can contact the service team directly: ',
  it_hotline: 'IT hotline: 010-********',
  hr_hotline: 'HR hotline: 010-********',
  work_time: 'Working hours: Monday to Friday 8:30~17:00',
  common_issues: 'Here are some common IT issues with quick access for you:',
  cannot_print_file: 'Unable to print file',
  email_expansion_application: 'Email expansion request',

  asset_no: 'Asset No',
  asset_name: 'Asset Name',
  category: 'Category',
  template: 'Template',
  inventory_status: 'Inventory Status',
  service_status: 'Service Status',
};
