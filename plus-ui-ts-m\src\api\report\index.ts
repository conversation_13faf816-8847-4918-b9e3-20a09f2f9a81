import request from '@/utils/request';
import type { AxiosResponse } from 'axios';
import { parseStrEmpty } from '@/utils/ruoyi';
import type { ReportBO, ReportVO, DistributionBO, DistributionVO, TrendBO, TrendVO, FolderVO } from './types';

// pc端固定客户端授权id
const clientId = import.meta.env.VITE_APP_CLIENT_ID;

/**
 * @param data {LoginData}
 * @returns
 */

export const getFolderWithReports = (projectId: number, parentFolderId?: number): Promise<AxiosResponse<FolderVO[]>>=> {
  let url = '/servicetrack/foldertree/getFolderWithReports?projectId=' + parseStrEmpty(projectId);
  if (parentFolderId !== undefined) {
    url += '&parentFolderId=' + parseStrEmpty(parentFolderId);
  }
  return request({
    url: url,
    method: 'get'
  });
};


export const getReportInfo = (projectId: number, reportId: number): Promise<AxiosResponse<ReportVO>> => {
  return request({
    url: '/servicetrack/report/info' + '?projectId=' + parseStrEmpty(projectId) + '&reportId=' + parseStrEmpty(reportId),
    method: 'get'
  });
};

export const addReport = (data: ReportBO): Promise<AxiosResponse<number>> => {
  return request({
    url: '/servicetrack/report/info',
    method: 'POST',
    data
  });
};

export const updateReport = (data: ReportBO): Promise<AxiosResponse<number>> => {
  return request({
    url: '/servicetrack/report/info',
    method: 'PUT',
    data
  });
};

export const deleteReport = (id: string | number): Promise<AxiosResponse<number>> => {
  return request({
    url: `/servicetrack/report/info/${id}`,
    method: 'DELETE',
  });
};

export const getDistribution = (query: DistributionBO): Promise<AxiosResponse<DistributionVO[]>> => {
  return request({
    url: '/servicetrack/report/distribution',
    method: 'get',
    params: query
  });
};

export const getTrend = (query: TrendBO): Promise<AxiosResponse<TrendVO[]>> => {
  return request({
    url: '/servicetrack/report/trend',
    method: 'get',
    params: query
  });
};


export default {
  getReportInfo,
  addReport,
  updateReport,
  deleteReport,
  getDistribution,
};

