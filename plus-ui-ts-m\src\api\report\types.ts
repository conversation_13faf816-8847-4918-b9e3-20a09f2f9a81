export interface FolderVO {
  id: string | number;
  parentFolder: number;
  childFolder: number;
  childFolderType: number;
  childFolderName: string;
  displayOrder: number;
  children: ChildVO[];
}

export interface ChildVO {
  childFolder: number;
  childFolderType: number;
  childFolderName: string;
}

export interface ReportBO {
  id?: string | number;
  projectId: number;
  reportId: number;
  reportType: number;
  targetProjectId: number;
  folderId: number;
  reportName: string;
  reportDescription: string;
  reportSetting?: string;
}

export interface ReportVO {
  id: string | number;
  projectId: number;
  reportId: number;
  reportType: number;
  targetProjectId: number;
  folderId: number;
  reportName: string;
  reportDescription: string;
  createdDate: string;
  createdBy: string;
  modifiedDate: string;
  modifiedBy: string;
  reportSetting: string;
}

export interface DistributionBO{
  projectId: string | number;
  reportId: number;
  keyword?: string;
  stateIds?: number[];
  ownerIds?: number[];
  distributionFieldId: number;
  DateFieldId: number;
}

export interface DistributionVO{
  choiceId: number;
  choiceName: string;
  itemCount: number;
}

export interface TrendBO{
  projectId: string | number;
  reportId: number;
  keyword?: string;
  stateIds?: number[];
  ownerIds?: number[];
  dateFieldId: number;
  dateRangeOption: number;
}

export interface TrendVO{
  trendTimePeriod: string;
  itemCount: number;
}