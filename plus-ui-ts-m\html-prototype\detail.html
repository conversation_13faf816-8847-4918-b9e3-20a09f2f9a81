<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工单详情 - ITSM系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background-color: #f5f7fa;
            width: 100%;
            max-width: 430px;
            margin: 0 auto;
            height: 100vh;
            overflow: hidden;
            position: relative;
            border-radius: 40px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        
        .app-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            background-color: white;
            border-radius: 40px;
        }
        
        .status-bar {
            height: 44px;
            width: 100%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            position: relative;
            z-index: 10;
            border-top-left-radius: 40px;
            border-top-right-radius: 40px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .tab-bar {
            height: 84px;
            width: 100%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-around;
            border-top: 1px solid rgba(0,0,0,0.05);
            padding-bottom: 20px;
            border-bottom-left-radius: 40px;
            border-bottom-right-radius: 40px;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 50px;
            color: #999;
        }
        
        .tab-item.active {
            color: #409EFF;
        }
        
        .content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 15px;
            background-color: #f5f7fa;
        }
        
        .card {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .badge-primary {
            background-color: rgba(64, 158, 255, 0.1);
            color: #409EFF;
        }
        
        .badge-warning {
            background-color: rgba(230, 162, 60, 0.1);
            color: #E6A23C;
        }
        
        .badge-danger {
            background-color: rgba(245, 108, 108, 0.1);
            color: #F56C6C;
        }
        
        .badge-success {
            background-color: rgba(103, 194, 58, 0.1);
            color: #67C23A;
        }
        
        .btn-primary {
            background-color: #409EFF;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 500;
            font-size: 16px;
            text-align: center;
            display: block;
        }
        
        .btn-outline {
            background-color: transparent;
            color: #409EFF;
            border: 1px solid #409EFF;
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 500;
            font-size: 16px;
            text-align: center;
            display: block;
        }
        
        .attachment {
            display: flex;
            align-items: center;
            background: #f9fafc;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        
        .progress-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 15px;
            background-color: #fff;
            border-radius: 12px;
            margin-bottom: 15px;
            cursor: pointer;
        }
        
        .progress-content {
            display: none;
            padding: 15px;
            background-color: #fff;
            border-radius: 12px;
            margin-bottom: 15px;
        }
        
        .timeline {
            position: relative;
            padding-left: 20px;
        }
        
        .timeline:before {
            content: '';
            position: absolute;
            left: 0;
            top: 10px;
            bottom: 0;
            width: 2px;
            background-color: #e0e0e0;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        
        .timeline-item:last-child {
            margin-bottom: 0;
        }
        
        .timeline-item:before {
            content: '';
            position: absolute;
            left: -25px;
            top: 4px;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: #409EFF;
            border: 2px solid #fff;
            z-index: 1;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="status-bar">
            <div class="flex items-center">
                <span class="text-black text-sm font-medium">9:41</span>
            </div>
            <div class="flex items-center space-x-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 10a6 6 0 0 0-12 0v8h12v-8z"/><path d="M20 10a8 8 0 0 0-16 0v8h2v-8a6 6 0 0 1 12 0v8h2v-8z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6.33 20.855A10.968 10.968 0 0 1 2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10c-1.97 0-3.823-.518-5.425-1.428"/><path d="M16 12V7"/><path d="M8 12v5"/><path d="M12 16v3"/><path d="M12 7v5"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12a10 10 0 0 1 10 -10v0a10 10 0 0 1 10 10v0a10 10 0 0 1 -10 10h-2a8 8 0 0 0 -8 -8"/><path d="M5 18v-2"/><path d="M2 6v-2h2"/><path d="M20 6v-2h-2"/><path d="M3 10h2"/><path d="M17 14h4"/></svg>
            </div>
        </div>
        
        <div class="flex items-center bg-white px-4 py-3 border-b border-gray-100">
            <a href="work-order.html" class="mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="19" y1="12" x2="5" y2="12"></line><polyline points="12 19 5 12 12 5"></polyline></svg>
            </a>
            <h1 class="text-lg font-bold flex-1">工单详情</h1>
            <div class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-600"><circle cx="12" cy="12" r="1"></circle><circle cx="19" cy="12" r="1"></circle><circle cx="5" cy="12" r="1"></circle></svg>
            </div>
        </div>
        
        <div class="content">
            <!-- 工单基本信息 -->
            <div class="card p-4 mb-4">
                <div class="flex justify-between items-start mb-3">
                    <div>
                        <h2 class="text-xl font-bold mb-1">网络连接问题</h2>
                        <div class="text-sm text-gray-500">工单号: WO-2023042501</div>
                    </div>
                    <span class="badge badge-warning">处理中</span>
                </div>
                
                <div class="mb-4">
                    <div class="grid grid-cols-2 gap-3 text-sm">
                        <div>
                            <div class="text-gray-500 mb-1">申请人</div>
                            <div class="flex items-center">
                                <div class="w-5 h-5 rounded-full bg-gray-300 mr-1 flex items-center justify-center overflow-hidden">
                                    <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=30&h=30&q=80" alt="用户头像">
                                </div>
                                <span>张三</span>
                            </div>
                        </div>
                        <div>
                            <div class="text-gray-500 mb-1">处理人</div>
                            <div class="flex items-center">
                                <div class="w-5 h-5 rounded-full bg-gray-300 mr-1 flex items-center justify-center overflow-hidden">
                                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=30&h=30&q=80" alt="处理人头像">
                                </div>
                                <span>IT支持 - 王明</span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <div class="text-gray-500 mb-1">优先级</div>
                            <div class="flex items-center">
                                <span class="w-2 h-2 rounded-full bg-yellow-500 mr-1"></span>
                                <span>中</span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <div class="text-gray-500 mb-1">创建时间</div>
                            <div>2023-04-25 14:30</div>
                        </div>
                    </div>
                </div>
                
                <div class="text-gray-500 text-sm mb-1">描述</div>
                <div class="text-sm text-gray-700 mb-4">
                    无法连接到公司内网，已经重启路由器但问题仍然存在。尝试了多次连接，每次都会提示"无法连接到网络"。其他同事的电脑可以正常连接，只有我的电脑不行。
                </div>
                
                <div class="text-gray-500 text-sm mb-2">附件</div>
                <div class="attachment">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#409EFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg>
                    <div class="flex-1">
                        <div class="text-sm font-medium">错误截图.png</div>
                        <div class="text-xs text-gray-500">1.2MB</div>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line></svg>
                </div>
            </div>
            
            <!-- 处理进度折叠面板 -->
            <div class="progress-toggle" onclick="toggleProgress()">
                <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#409EFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                        <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
                    </svg>
                    <span class="font-medium">处理进度</span>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="transform transition-transform" id="progress-arrow">
                    <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
            </div>
            
            <div class="progress-content" id="progress-content">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="text-sm font-medium">创建工单</div>
                        <div class="text-xs text-gray-500 mb-1">2023-04-25 14:30</div>
                        <div class="text-sm text-gray-600">张三提交了工单</div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="text-sm font-medium">分配工单</div>
                        <div class="text-xs text-gray-500 mb-1">2023-04-25 14:45</div>
                        <div class="text-sm text-gray-600">系统自动分配给 IT支持 - 王明</div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="text-sm font-medium">开始处理</div>
                        <div class="text-xs text-gray-500 mb-1">2023-04-25 15:20</div>
                        <div class="text-sm text-gray-600">IT支持 - 王明 开始处理工单</div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="text-sm font-medium">回复</div>
                        <div class="text-xs text-gray-500 mb-1">2023-04-25 15:35</div>
                        <div class="text-sm text-gray-600">IT支持 - 王明：请提供您的电脑型号和IP地址，我们会尽快为您解决问题。</div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="text-sm font-medium">回复</div>
                        <div class="text-xs text-gray-500 mb-1">2023-04-25 15:42</div>
                        <div class="text-sm text-gray-600">张三：电脑型号是ThinkPad T480，IP地址显示为************</div>
                    </div>
                    
                    <div class="timeline-item">
                        <div class="text-sm font-medium">当前状态</div>
                        <div class="text-xs text-gray-500 mb-1">2023-04-25 16:10</div>
                        <div class="text-sm text-gray-600">IT支持 - 王明：我已经初步检查了网络配置，可能是IP地址冲突导致的问题。我会在今天下午安排技术人员前往您的工位现场处理。</div>
                    </div>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button class="flex-1 btn-primary">操作</button>
                <button class="flex-1 btn-outline">编辑</button>
            </div>
        </div>
        
        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <a href="home.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                    <polyline points="9 22 9 12 15 12 15 22"></polyline>
                </svg>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="modules.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="3" y="3" width="7" height="7"></rect>
                    <rect x="14" y="3" width="7" height="7"></rect>
                    <rect x="14" y="14" width="7" height="7"></rect>
                    <rect x="3" y="14" width="7" height="7"></rect>
                </svg>
                <span class="text-xs mt-1">模块</span>
            </a>
            <a href="reports.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="12" y1="20" x2="12" y2="10"></line>
                    <line x1="18" y1="20" x2="18" y2="4"></line>
                    <line x1="6" y1="20" x2="6" y2="16"></line>
                </svg>
                <span class="text-xs mt-1">报表</span>
            </a>
            <a href="profile.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                </svg>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
    
    <script>
        function toggleProgress() {
            const content = document.getElementById('progress-content');
            const arrow = document.getElementById('progress-arrow');
            
            if (content.style.display === 'none' || !content.style.display) {
                content.style.display = 'block';
                arrow.classList.add('rotate-180');
            } else {
                content.style.display = 'none';
                arrow.classList.remove('rotate-180');
            }
        }
    </script>
</body>
</html>
