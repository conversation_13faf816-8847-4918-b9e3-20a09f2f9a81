<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - ITSM系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="public/static/font/iconfont.css">
    <style>
        @font-face {
            font-family: 'AlibabaPuHuiTi';
            src: url('public/static/font/AlibabaPuHuiTi-3-45-Light.ttf') format('ttf');
            font-weight: 300;
            font-style: normal;
        }
        @font-face {
            font-family: 'AlibabaPuHuiTi';
            src: url('public/static/font/AlibabaPuHuiTi-3-55-Regular.ttf') format('ttf');
            font-weight: 400;
            font-style: normal;
        }
        @font-face {
            font-family: 'AlibabaPuHuiTi';
            src: url('public/static/font/AlibabaPuHuiTi-3-65-Medium.ttf') format('ttf');
            font-weight: 500;
            font-style: normal;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'AlibabaPuHuiTi', -apple-system, BlinkMacSystemFont, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background-color: #f5f7fa;
            width: 100%;
            max-width: 430px;
            margin: 0 auto;
            height: 100vh;
            overflow: hidden;
            position: relative;
            border-radius: 40px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        
        .app-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            background-color: white;
            border-radius: 40px;
        }
        
        .status-bar {
            height: 44px;
            width: 100%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            position: relative;
            z-index: 10;
            border-top-left-radius: 40px;
            border-top-right-radius: 40px;
        }
        
        .btn-primary {
            background-color: #409EFF;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 500;
            font-size: 16px;
            text-align: center;
            width: 100%;
            display: block;
        }
        
        .input-field {
            width: 100%;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s;
            background: #f9fafc;
        }
        
        .input-field:focus {
            border-color: #409EFF;
            outline: none;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
        
        .content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 20px;
        }
        
        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 50;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        .modal-container {
            background-color: white;
            width: 90%;
            max-width: 380px;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(20px);
            transition: all 0.3s ease;
        }
        
        .modal-overlay.active .modal-container {
            transform: translateY(0);
        }
        
        .login-option {
            padding: 16px;
            border: 1px solid #eaeaea;
            border-radius: 12px;
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .login-option:hover {
            border-color: #409EFF;
            background-color: #f0f8ff;
        }
        
        .login-option .icon-container {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background-color: #ecf5ff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
        }
        
        .btn-modal {
            background-color: #409EFF;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 0;
            font-weight: 500;
            font-size: 16px;
            text-align: center;
            width: 100%;
            display: block;
            cursor: pointer;
        }
        
        .close-button {
            position: absolute;
            top: 16px;
            right: 16px;
            background: none;
            border: none;
            cursor: pointer;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s;
        }
        
        .close-button:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="status-bar">
            <div class="flex items-center">
                <span class="text-black text-sm font-medium">9:41</span>
            </div>
            <div class="flex items-center space-x-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 10a6 6 0 0 0-12 0v8h12v-8z"/><path d="M20 10a8 8 0 0 0-16 0v8h2v-8a6 6 0 0 1 12 0v8h2v-8z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6.33 20.855A10.968 10.968 0 0 1 2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10c-1.97 0-3.823-.518-5.425-1.428"/><path d="M16 12V7"/><path d="M8 12v5"/><path d="M12 16v3"/><path d="M12 7v5"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12a10 10 0 0 1 10 -10v0a10 10 0 0 1 10 10v0a10 10 0 0 1 -10 10h-2a8 8 0 0 0 -8 -8"/><path d="M5 18v-2"/><path d="M2 6v-2h2"/><path d="M20 6v-2h-2"/><path d="M3 10h2"/><path d="M17 14h4"/></svg>
            </div>
        </div>
        
        <div class="content">
            <div class="flex flex-col justify-between h-full">
                <div>
                    <div class="flex justify-center mb-8 mt-6">
                        <img src="https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-1.2.1&auto=format&fit=crop&w=180&q=80" alt="ITSM系统" class="rounded-2xl w-24">
                    </div>
                    
                    <h1 class="text-2xl font-bold mb-2">欢迎回来</h1>
                    <p class="text-gray-500 mb-6">请登录您的账号以继续</p>
                    
                    <div id="loginForm">
                        <div class="mb-4">
                            <label for="username" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                            <input type="text" id="username" class="input-field" placeholder="请输入用户名" autocomplete="off">
                        </div>
                        
                        <div class="mb-6">
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
                            <input type="password" id="password" class="input-field" placeholder="请输入密码">
                        </div>
                        
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center">
                                <input type="checkbox" id="remember" class="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500">
                                <label for="remember" class="ml-2 text-sm text-gray-600">记住我</label>
                            </div>
                            <a href="#" class="text-sm text-blue-600 hover:underline">忘记密码?</a>
                        </div>
                        
                        <button id="loginButton" class="btn-primary mb-4">登录</button>
                    </div>
                </div>
                
                <div class="text-center my-6">
                    <p class="text-sm text-gray-500">还没有账号? <a href="#" class="text-blue-600 hover:underline">注册</a></p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 登录方式选择弹窗 -->
    <div id="loginModal" class="modal-overlay">
        <div class="modal-container relative">
            <button id="closeModal" class="close-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
            </button>
            
            <h2 class="text-2xl font-bold mb-6 text-center">选择登录门户</h2>
            
            <div id="userPortalOption" class="login-option">
                <div class="icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="#409EFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                </div>
                <div>
                    <h3 class="font-medium">用户门户</h3>
                    <p class="text-sm text-gray-500">普通用户访问</p>
                </div>
            </div>
            
            <div id="engineerPortalOption" class="login-option">
                <div class="icon-container">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="#409EFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24"><path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path></svg>
                </div>
                <div>
                    <h3 class="font-medium">工程师门户</h3>
                    <p class="text-sm text-gray-500">服务支持团队访问</p>
                </div>
            </div>
            
            <button id="confirmLoginType" class="btn-modal mt-4">确定</button>
        </div>
    </div>
    
    <script>
        // 获取DOM元素
        const loginButton = document.getElementById('loginButton');
        const loginModal = document.getElementById('loginModal');
        const closeModal = document.getElementById('closeModal');
        const confirmLoginType = document.getElementById('confirmLoginType');
        const userPortalOption = document.getElementById('userPortalOption');
        const engineerPortalOption = document.getElementById('engineerPortalOption');
        
        // 当前登录类型（默认用户）
        let currentLoginType = 'user';
        let selectedOption = null;
        
        // 打开登录选择弹窗
        loginButton.addEventListener('click', function(e) {
            e.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if(username && password) {
                // 显示登录选择弹窗
                loginModal.classList.add('active');
                // 重置选择
                resetSelections();
            } else {
                alert('请输入用户名和密码');
            }
        });
        
        // 关闭弹窗
        closeModal.addEventListener('click', function() {
            loginModal.classList.remove('active');
        });
        
        // 选择用户门户
        userPortalOption.addEventListener('click', function() {
            resetSelections();
            userPortalOption.style.borderColor = '#409EFF';
            userPortalOption.style.backgroundColor = '#ecf5ff';
            currentLoginType = 'user';
            selectedOption = userPortalOption;
        });
        
        // 选择工程师门户
        engineerPortalOption.addEventListener('click', function() {
            resetSelections();
            engineerPortalOption.style.borderColor = '#409EFF';
            engineerPortalOption.style.backgroundColor = '#ecf5ff';
            currentLoginType = 'engineer';
            selectedOption = engineerPortalOption;
        });
        
        // 重置选项样式
        function resetSelections() {
            userPortalOption.style.borderColor = '#eaeaea';
            userPortalOption.style.backgroundColor = 'white';
            engineerPortalOption.style.borderColor = '#eaeaea';
            engineerPortalOption.style.backgroundColor = 'white';
            selectedOption = null;
        }
        
        // 确认登录类型并跳转
        confirmLoginType.addEventListener('click', function() {
            if (!selectedOption) {
                alert('请选择登录门户');
                return;
            }
            
            // 根据不同的登录类型跳转到不同的页面
            if (currentLoginType === 'user') {
                window.location.href = 'home.html';
            } else {
                window.location.href = 'home.html?role=engineer';
            }
        });
    </script>
</body>
</html> 