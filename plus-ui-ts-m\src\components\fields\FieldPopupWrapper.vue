<template>
  <van-popup 
    v-model:show="show"
    :position="position"
    :style="style"
    :round="round"
    safe-area-inset-bottom
  >
    <div class="popup-container" :class="{ 'full-height': fullHeight }">
      <van-nav-bar
        :title="title"
        left-text="取消"
        right-text="确认"
        @click-left="cancel"
        @click-right="confirm"
      />
      <div class="popup-content" :class="{ 'flex-1': fullHeight }">
        <slot></slot>
      </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  show: boolean
  title?: string
  position?: 'bottom' | 'right' | 'top' | 'left'
  fullHeight?: boolean
  round?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  position: 'bottom',
  fullHeight: false,
  round: false
})

const emit = defineEmits<{ 
  (e: 'update:show', value: boolean): void,
  (e: 'confirm'): void,
  (e: 'cancel'): void
}>()

const show = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const style = computed(() => {
  if (props.position === 'right') {
    return { width: '100%', height: '100%' }
  } else if (props.position === 'bottom') {
    return { width: '100%', height: 'auto' }
  }
  return {}
})

const confirm = () => {
  emit('confirm')
}

const cancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.popup-container {
  width: 100vw;
  background-color: #fff;
}

.popup-container.full-height {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-content {
  padding: 16px;
}

.popup-content.flex-1 {
  flex: 1;
  overflow: hidden;
}

.popup-content :deep(.van-field__word-limit) {
  text-align: right;
  margin-top: 4px;
}
</style>