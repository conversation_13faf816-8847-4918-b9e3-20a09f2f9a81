<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ITSM系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="public/static/font/iconfont.css">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        @font-face {
            font-family: 'AlibabaPuHuiTi';
            src: url('public/static/font/AlibabaPuHuiTi-3-45-Light.ttf') format('ttf');
            font-weight: 300;
            font-style: normal;
        }
        @font-face {
            font-family: 'AlibabaPuHuiTi';
            src: url('public/static/font/AlibabaPuHuiTi-3-55-Regular.ttf') format('ttf');
            font-weight: 400;
            font-style: normal;
        }
        @font-face {
            font-family: 'AlibabaPuHuiTi';
            src: url('public/static/font/AlibabaPuHuiTi-3-65-Medium.ttf') format('ttf');
            font-weight: 500;
            font-style: normal;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'AlibabaPuHuiTi', -apple-system, BlinkMacSystemFont, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background-color: #f5f7fa;
            width: 100%;
            max-width: 430px;
            margin: 0 auto;
            height: 100vh;
            overflow: hidden;
            position: relative;
            border-radius: 40px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        
        .app-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            background-color: white;
            border-radius: 40px;
        }
        
        .status-bar {
            height: 44px;
            width: 100%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            position: relative;
            z-index: 10;
            border-top-left-radius: 40px;
            border-top-right-radius: 40px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .tab-bar {
            height: 84px;
            width: 100%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-around;
            border-top: 1px solid rgba(0,0,0,0.05);
            padding-bottom: 20px;
            border-bottom-left-radius: 40px;
            border-bottom-right-radius: 40px;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 50px;
            color: #999;
        }
        
        .tab-item.active {
            color: #409EFF;
        }
        
        .content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 15px;
        }
        
        .btn-primary {
            background-color: #409EFF;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 500;
            font-size: 16px;
            text-align: center;
            width: 100%;
            display: block;
        }
        
        .btn-outline {
            background-color: transparent;
            color: #409EFF;
            border: 1px solid #409EFF;
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 500;
            font-size: 16px;
            text-align: center;
            width: 100%;
            display: block;
        }
        
        .login-card {
            background-color: #fff;
            border-radius: 12px;
            border: 1px solid rgba(0,0,0,0.08);
            padding: 20px;
            transition: all 0.3s;
            cursor: pointer;
            margin-bottom: 16px;
        }
        
        .login-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
            border-color: #409EFF;
        }
        
        .login-card.active {
            border-color: #409EFF;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="status-bar">
            <div class="flex items-center">
                <span class="text-black text-sm font-medium">9:41</span>
            </div>
            <div class="flex items-center space-x-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 10a6 6 0 0 0-12 0v8h12v-8z"/><path d="M20 10a8 8 0 0 0-16 0v8h2v-8a6 6 0 0 1 12 0v8h2v-8z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6.33 20.855A10.968 10.968 0 0 1 2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10c-1.97 0-3.823-.518-5.425-1.428"/><path d="M16 12V7"/><path d="M8 12v5"/><path d="M12 16v3"/><path d="M12 7v5"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12a10 10 0 0 1 10 -10v0a10 10 0 0 1 10 10v0a10 10 0 0 1 -10 10h-2a8 8 0 0 0 -8 -8"/><path d="M5 18v-2"/><path d="M2 6v-2h2"/><path d="M20 6v-2h-2"/><path d="M3 10h2"/><path d="M17 14h4"/></svg>
            </div>
        </div>
        
        <div class="content">
            <div class="flex flex-col items-center justify-center h-full text-center">
                <img src="https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-1.2.1&auto=format&fit=crop&w=320&q=80" alt="ITSM系统" class="rounded-2xl mb-6 w-40">
                <h1 class="text-3xl font-bold mb-3">ITSM系统</h1>
                <p class="text-gray-500 mb-6">高效的IT服务管理解决方案</p>
                
                <h2 class="text-xl font-medium mb-4">请选择登录方式</h2>
                
                <div class="w-full px-4">
                    <div id="userLogin" class="login-card flex items-center">
                        <div class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="#409EFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                        </div>
                        <div class="flex-1 text-left">
                            <h3 class="font-medium">用户登录</h3>
                            <p class="text-sm text-gray-500">普通用户访问</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
                    </div>
                    
                    <div id="engineerLogin" class="login-card flex items-center">
                        <div class="w-12 h-12 rounded-full bg-blue-50 flex items-center justify-center mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="#409EFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24"><path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path></svg>
                        </div>
                        <div class="flex-1 text-left">
                            <h3 class="font-medium">工程师登录</h3>
                            <p class="text-sm text-gray-500">服务支持团队访问</p>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
                    </div>
                </div>
                
                <p class="text-sm text-gray-400 mt-8">v1.0.0</p>
            </div>
        </div>
        
        <div class="tab-bar">
            <a href="home.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="work-order.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
                <span class="text-xs mt-1">工单</span>
            </a>
            <a href="dashboard.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24"><rect x="3" y="3" width="7" height="7"></rect><rect x="14" y="3" width="7" height="7"></rect><rect x="14" y="14" width="7" height="7"></rect><rect x="3" y="14" width="7" height="7"></rect></svg>
                <span class="text-xs mt-1">看板</span>
            </a>
            <a href="profile.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
    
    <script>
        document.getElementById('userLogin').addEventListener('click', function() {
            window.location.href = 'login.html?type=user';
        });
        
        document.getElementById('engineerLogin').addEventListener('click', function() {
            window.location.href = 'login.html?type=engineer';
        });

        // 自动跳转到登录页
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 2000);
    </script>
</body>
</html> 