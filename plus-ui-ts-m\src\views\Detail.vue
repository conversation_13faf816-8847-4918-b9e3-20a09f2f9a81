<template>
  <div class="page-container" :class="{ 'show-scrollbar': showScrollbar }">
    <!-- 顶部栏 -->
    <van-nav-bar title="工单详情" left-arrow @click-left="$router.back()" fixed />

    <!-- 工单基本信息 -->
    <div class="detail-header">
      <div class="card-header">
        <div class="title-section">
          <!-- 第一行：工单号 -->
          <div class="work-number">工单号: {{ workOrderData.number }}</div>
          
          <!-- 第二行：标题 -->
          <div class="title-row">
            <h2 class="work-title">{{ workOrderData.title }}</h2>
          </div>
          
          <!-- 第三行：左边显示状态，右边显示负责人头像 -->
          <div class="status-owner-row">
            <div class="status-container">
              <span class="status-badge" :class="`status-${workOrderData.status}`" @click="showStatusPopup = true">{{ workOrderData.statusText }}</span>
            </div>
            <div class="owner-container">
              <div class="owner-avatar" @click="showOwnerPopup = true">
                <img :src="workOrderData.owner.avatar" :alt="workOrderData.owner.name" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
      
     
      <!-- 字段列表 -->
      <van-collapse v-model="fieldListActiveNames" class="detail-card">
        <van-collapse-item title="字段列表1" name="fieldList1">
          <van-cell-group
            :border="false"
          >
            <van-cell 
              v-for="field in workOrderFields" 
              :key="field.id"
              :title="field.label"
              class="vertical-cell"
            >
              <template #default>
                <div class="field-value-vertical">
                  <!-- 用户类型字段显示头像 -->
                  <template v-if="isUserField(field)">
                    <div class="user-info">
                      <img class="avatar-small" :src="(field.value as any).avatar" />
                      <span>{{ (field.value as any).name }}</span>
                    </div>
                  </template>
                  
                  <!-- 单行文本输入框 (SHORT_TEXT) - 可直接编辑 -->
                      <template v-else-if="field.type === FieldTypeEnum.SHORT_TEXT">
                        <van-field
                          :model-value="field.value as string"
                          @update:model-value="val => field.value = val"
                          :placeholder="`请输入${field.label}`"
                          @blur="saveField(field)"
                          @keyup.enter="saveField(field)"
                          class="editable-field field-value consistent-height"
                          :data-field-id="field.id"
                          :style="{ display: editableFieldId === field.id ? 'block' : 'none' }"
                        />
                        <div 
                          v-show="editableFieldId !== field.id"
                          class="normal-value editable-field-container field-value-left consistent-height" 
                          @click="editField(field, $event as any)"
                          :data-field-id="field.id"
                        >
                        <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                        <span v-else class="empty-value">空</span>
                        </div>
                      </template>
                  
                  <!-- 多行文本输入框 (PLAIN_TEXT) - 点击弹出popup -->
                    <template v-else-if="field.type === FieldTypeEnum.PLAIN_TEXT">
                      <div 
                        class="normal-value editable-field-container field-value-left consistent-height"
                        @click="editField(field, $event as any)"
                      >
                        <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                        <span v-else class="empty-value">空</span>
                      </div>
                    </template>

                  <!-- 单选下拉框 (DROPDOWN_LIST) - 点击弹出popup -->
                    <template v-else-if="field.type === FieldTypeEnum.DROPDOWN_LIST">
                      <div 
                        class="normal-value editable-field-container field-value-left consistent-height"
                        @click="editField(field, $event as any)"
                      >
                        <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                        <span v-else class="empty-value">空</span>
                      </div>
                    </template>
                  
                  <!-- 多选框 (MULTIPLE_SELECTION_LISTBOX) - 点击弹出popup -->
                    <template v-else-if="field.type === FieldTypeEnum.MULTIPLE_SELECTION_LISTBOX">
                      <div 
                        class="normal-value editable-field-container field-value-left consistent-height"
                        @click="editField(field, $event as any)"
                      >
                        <div class="tags-container">
                          <span v-for="tag in field.value" :key="tag" class="tag">{{ tag }}</span>
                        </div>
                      </div>
                      
                    </template>

                  <!-- 多行输入框,富文本输入框 (MULTILINE_EDIT_BOX) - 点击弹出popup -->
                    <template v-else-if="field.type === FieldTypeEnum.MULTILINE_EDIT_BOX">
                      <div 
                        class="normal-value editable-field-container field-value-left consistent-height"
                        @click="editField(field, $event as any)"
                      >
                        <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                        <span v-else class="empty-value">空</span>
                      </div>
                    </template>

                  <!-- 日期时间字段 (DATE_TIME_FIELD) - 点击弹出popup -->
                    <template v-else-if="field.type === FieldTypeEnum.DATE_TIME_FIELD">
                      <div 
                        class="normal-value editable-field-container field-value-left consistent-height"
                        @click="editField(field, $event as any)"
                      >
                        <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                        <span v-else class="empty-value">空</span>
                      </div>
                    </template>
                  
                  <!-- 复选框字段 (CHECKBOX) - 点击弹出popup -->
                    <template v-else-if="field.type === FieldTypeEnum.CHECKBOX">
                      <div 
                        class="normal-value editable-field-container field-value-left consistent-height"
                        @click="editField(field, $event as any)"
                      >
                        <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                        <span v-else class="empty-value">空</span>
                      </div>
                    </template>

                  <!-- 单选按钮字段 (RADIO_BUTTON) - 点击弹出popup -->
                    <template v-else-if="field.type === FieldTypeEnum.RADIO_BUTTON">
                      <div 
                        class="normal-value editable-field-container field-value-left consistent-height"
                        @click="editField(field, $event as any)"
                      >
                        <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                        <span v-else class="empty-value">空</span>
                      </div>
                    </template>

                  <!-- 组合框字段 (COMBOBOX) - 点击弹出popup -->
                    <template v-else-if="field.type === FieldTypeEnum.COMBOBOX">
                      <div 
                        class="normal-value editable-field-container field-value-left consistent-height"
                        @click="editField(field, $event as any)"
                      >
                        <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                        <span v-else class="empty-value">空</span>
                      </div>
                    </template>

                  <!-- 金额字段 -->
                    <template v-else-if="field.type === FieldTypeEnum.AMOUNT">
                      <div 
                        class="normal-value editable-field-container field-value-left consistent-height"
                        @click="editField(field, $event as any)"
                      >
                        <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                        <span v-else class="empty-value">0.00</span>
                      </div>
                    </template>

                  <!-- 附件类型字段 -->
                  <template v-else-if="isAttachmentField(field)">
                    <div class="attachment-item">
                      <van-icon name="description" class="attachment-icon" />
                      <div class="attachment-info">
                        <div class="attachment-name">{{ (field.value as any)[0].name }}</div>
                        <div class="attachment-size">{{ (field.value as any)[0].size }}</div>
                      </div>
                      <van-icon name="down" class="attachment-download" />
                    </div>
                  </template>

                  <!-- 其他字段 -->
                    <template v-else>
                      <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                      <span v-else class="empty-value">无</span>
                    </template>
                </div>
              </template>
            </van-cell>
          </van-cell-group>
        </van-collapse-item>
      </van-collapse>

       <van-collapse v-model="fieldListActiveNames" class="detail-card">
        <van-collapse-item title="字段列表2" name="fieldList2">
          <van-cell-group
            :border="false"
          >
            <van-cell 
              v-for="field in workOrderFields" 
              :key="field.id"
              :title="field.label"
              class="vertical-cell"
            >
              <template #default>
                <div class="field-value-vertical">
                  <!-- 用户类型字段显示头像 -->
                  <template v-if="isUserField(field)">
                    <div class="user-info">
                      <img class="avatar-small" :src="(field.value as any).avatar" />
                      <span>{{ (field.value as any).name }}</span>
                    </div>
                  </template>
                  
                  <!-- 单行文本输入框 (SHORT_TEXT) - 可直接编辑 -->
                      <template v-else-if="field.type === FieldTypeEnum.SHORT_TEXT">
                        <van-field
                          :model-value="field.value as string"
                          @update:model-value="val => field.value = val"
                          :placeholder="`请输入${field.label}`"
                          @blur="saveField(field)"
                          @keyup.enter="saveField(field)"
                          class="editable-field field-value consistent-height"
                          :data-field-id="field.id"
                          :style="{ display: editableFieldId === field.id ? 'block' : 'none' }"
                        />
                        <div 
                          v-show="editableFieldId !== field.id"
                          class="normal-value editable-field-container field-value-left consistent-height" 
                          @click="editField(field, $event as any)"
                          :data-field-id="field.id"
                        >
                        <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                        <span v-else class="empty-value">空</span>
                        </div>
                      </template>
                  
                  <!-- 多行文本输入框 (PLAIN_TEXT) - 点击弹出popup -->
                    <template v-else-if="field.type === FieldTypeEnum.PLAIN_TEXT">
                      <div 
                        class="normal-value editable-field-container field-value-left consistent-height"
                        @click="editField(field, $event as any)"
                      >
                        <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                        <span v-else class="empty-value">空</span>
                      </div>
                    </template>

                  <!-- 单选下拉框 (DROPDOWN_LIST) - 点击弹出popup -->
                    <template v-else-if="field.type === FieldTypeEnum.DROPDOWN_LIST">
                      <div 
                        class="normal-value editable-field-container field-value-left consistent-height"
                        @click="editField(field, $event as any)"
                      >
                        <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                        <span v-else class="empty-value">空</span>
                      </div>
                    </template>
                  
                  <!-- 多选框 (MULTIPLE_SELECTION_LISTBOX) - 点击弹出popup -->
                    <template v-else-if="field.type === FieldTypeEnum.MULTIPLE_SELECTION_LISTBOX">
                      <div 
                        class="normal-value editable-field-container field-value-left consistent-height"
                        @click="editField(field, $event as any)"
                      >
                        <div class="tags-container">
                          <span v-for="tag in field.value" :key="tag" class="tag">{{ tag }}</span>
                        </div>
                      </div>
                      
                    </template>

                  <!-- 多行输入框,富文本输入框 (MULTILINE_EDIT_BOX) - 点击弹出popup -->
                    <template v-else-if="field.type === FieldTypeEnum.MULTILINE_EDIT_BOX">
                      <div 
                        class="normal-value editable-field-container field-value-left consistent-height"
                        @click="editField(field, $event as any)"
                      >
                        <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                        <span v-else class="empty-value">空</span>
                      </div>
                    </template>

                  <!-- 日期时间字段 (DATE_TIME_FIELD) - 点击弹出popup -->
                    <template v-else-if="field.type === FieldTypeEnum.DATE_TIME_FIELD">
                      <div 
                        class="normal-value editable-field-container field-value-left consistent-height"
                        @click="editField(field, $event as any)"
                      >
                        <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                        <span v-else class="empty-value">空</span>
                      </div>
                    </template>
                  
                  <!-- 复选框字段 (CHECKBOX) - 点击弹出popup -->
                    <template v-else-if="field.type === FieldTypeEnum.CHECKBOX">
                      <div 
                        class="normal-value editable-field-container field-value-left consistent-height"
                        @click="editField(field, $event as any)"
                      >
                        <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                        <span v-else class="empty-value">空</span>
                      </div>
                    </template>

                  <!-- 单选按钮字段 (RADIO_BUTTON) - 点击弹出popup -->
                    <template v-else-if="field.type === FieldTypeEnum.RADIO_BUTTON">
                      <div 
                        class="normal-value editable-field-container field-value-left consistent-height"
                        @click="editField(field, $event as any)"
                      >
                        <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                        <span v-else class="empty-value">空</span>
                      </div>
                    </template>

                  <!-- 组合框字段 (COMBOBOX) - 点击弹出popup -->
                    <template v-else-if="field.type === FieldTypeEnum.COMBOBOX">
                      <div 
                        class="normal-value editable-field-container field-value-left consistent-height"
                        @click="editField(field, $event as any)"
                      >
                        <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                        <span v-else class="empty-value">空</span>
                      </div>
                    </template>

                  <!-- 金额字段 -->
                    <template v-else-if="field.type === FieldTypeEnum.AMOUNT">
                      <div 
                        class="normal-value editable-field-container field-value-left consistent-height"
                        @click="editField(field, $event as any)"
                      >
                        <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                        <span v-else class="empty-value">0.00</span>
                      </div>
                    </template>

                  <!-- 附件类型字段 -->
                  <template v-else-if="isAttachmentField(field)">
                    <div class="attachment-item">
                      <van-icon name="description" class="attachment-icon" />
                      <div class="attachment-info">
                        <div class="attachment-name">{{ (field.value as any)[0].name }}</div>
                        <div class="attachment-size">{{ (field.value as any)[0].size }}</div>
                      </div>
                      <van-icon name="down" class="attachment-download" />
                    </div>
                  </template>

                  <!-- 其他字段 -->
                    <template v-else>
                      <span v-if="field.value" class="normal-value">{{ field.value }}</span>
                      <span v-else class="empty-value">无</span>
                    </template>
                </div>
              </template>
            </van-cell>
          </van-cell-group>
        </van-collapse-item>
      </van-collapse>


    <!-- 处理进度折叠面板 -->
    <van-collapse v-model="activeNames" class="detail-card">
      <van-collapse-item title="处理进度" name="progress">
        <ItemHistory :project-id="Number(projectId)" :item-id="Number(itemId)" />
      </van-collapse-item>
    </van-collapse>

    <!-- 底部导航栏 -->
    <van-tabbar route fixed>
      <van-tabbar-item to="/home" icon="home-o">首页</van-tabbar-item>
      <van-tabbar-item to="/projects" icon="apps-o">项目</van-tabbar-item>
      <van-tabbar-item to="/reports" icon="chart-trending-o">报表</van-tabbar-item>
      <van-tabbar-item to="/profile" icon="user-o">我的</van-tabbar-item>
    </van-tabbar>
  </div>
  
  <!-- PLAIN_TEXT 字段编辑弹窗 -->
  <van-popup 
    v-model:show="showPlainTextPopup" 
    position="right" 
    :style="{ width: '100%', height: '100%' }" 
    safe-area-inset-bottom>
    <div class="popup-container">
      <van-nav-bar
        :title="currentPlainTextfield?.label"
        left-text="取消"
        right-text="确认"
        @click-left="showPlainTextPopup = false"
        @click-right="savePlainTextValue"
      />
      <div class="popup-content">
        <van-field
          v-model="plainTextValue"
          type="textarea"
          rows="6"
          autosize
          :placeholder="`请输入${currentPlainTextfield?.label}`"
        />
      </div>
    </div>
  </van-popup>
  
  <!-- DROPDOWN_LIST 字段编辑弹窗 -->
  <van-popup 
    v-model:show="showDropdownPopup" 
    position="bottom" 
    :style="{ width: '100%', height: 'auto' }" 
    safe-area-inset-bottom
    round>
    <div class="popup-container">
      <van-nav-bar
        :title="currentDropdownField?.label"
        left-text="取消"
        right-text="确认"
        @click-left="showDropdownPopup = false"
        @click-right="saveDropdownValue"
      />
      <div class="popup-content">
        <van-radio-group v-model="selectedDropdownValue" class="radio-group">
          <van-cell-group>
            <van-cell 
              v-for="(option, index) in dropdownOptions" 
              :key="index" 
              clickable 
              @click="selectedDropdownValue = option.value">
              <div class="radio-cell">
                <van-radio :name="option.value" />
                <span class="radio-label">{{ option.label }}</span>
              </div>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </div>
  </van-popup>
  
  <!-- MULTIPLE_SELECTION_LISTBOX 字段编辑弹窗 -->
  <van-popup 
    v-model:show="showMultipleSelectionPopup" 
    position="bottom" 
    :style="{ width: '100%', height: 'auto' }" 
    safe-area-inset-bottom
    round>
    <div class="popup-container">
      <van-nav-bar
        :title="currentMultipleSelectionField?.label"
        left-text="取消"
        right-text="确认"
        @click-left="showMultipleSelectionPopup = false"
        @click-right="saveMultipleSelectionValue"
      />
      <div class="popup-content">
        <van-checkbox-group v-model="selectedMultipleValues" class="checkbox-group">
          <van-cell-group>
            <van-cell 
              v-for="(option, index) in multipleSelectionOptions" 
              :key="index" 
              clickable>
              <div class="checkbox-cell">
                <van-checkbox :name="option.value" />
                <span class="checkbox-label">{{ option.label }}</span>
              </div>
            </van-cell>
          </van-cell-group>
        </van-checkbox-group>
      </div>
    </div>
  </van-popup>
  
  <!-- MULTILINE_EDIT_BOX 字段编辑弹窗 -->
  <van-popup 
    v-model:show="showMultilineEditBoxPopup" 
    position="right" 
    :style="{ width: '100%', height: '100%' }" 
    safe-area-inset-bottom>
    <div class="popup-container full-height">
      <van-nav-bar
        :title="currentMultilineEditBoxField?.label"
        left-text="取消"
        right-text="确认"
        @click-left="showMultilineEditBoxPopup = false"
        @click-right="saveMultilineEditBoxValue"
      />
      <div class="popup-content flex-1">
        <van-field
          v-model="multilineEditBoxValue"
          type="textarea"
          rows="10"
          :placeholder="`请输入${currentMultilineEditBoxField?.label}`"
          class="full-height-field"
        />
      </div>
    </div>
  </van-popup>
  
  <!-- CHECKBOX 字段编辑弹窗 -->
  <van-popup 
    v-model:show="showCheckboxPopup" 
    position="bottom" 
    :style="{ width: '100%', height: 'auto' }" 
    safe-area-inset-bottom
    round>
    <div class="popup-container">
      <van-nav-bar
        :title="currentCheckboxField?.label"
        left-text="取消"
        right-text="确认"
        @click-left="showCheckboxPopup = false"
        @click-right="saveCheckboxValue"
      />
      <div class="popup-content">
        <van-radio-group v-model="selectedCheckboxValue" class="radio-group">
          <van-cell-group>
            <van-cell 
              v-for="(option, index) in checkboxOptions" 
              :key="index" 
              clickable 
              @click="selectedCheckboxValue = option.value">
              <div class="radio-cell">
                <van-radio :name="option.value" />
                <span class="radio-label">{{ option.label }}</span>
              </div>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </div>
  </van-popup>

  <!-- RADIO_BUTTON 字段编辑弹窗 -->
  <van-popup 
    v-model:show="showRadioPopup" 
    position="bottom" 
    :style="{ width: '100%', height: 'auto' }" 
    safe-area-inset-bottom
    round>
    <div class="popup-container">
      <van-nav-bar
        :title="currentRadioField?.label"
        left-text="取消"
        right-text="确认"
        @click-left="showRadioPopup = false"
        @click-right="saveRadioValue"
      />
      <div class="popup-content">
        <van-radio-group v-model="selectedRadioValue" class="radio-group">
          <van-cell-group>
            <van-cell 
              v-for="(option, index) in RadioOptions" 
              :key="index" 
              clickable 
              @click="selectedRadioValue = option.value">
              <div class="radio-cell">
                <van-radio :name="option.value" />
                <span class="radio-label">{{ option.label }}</span>
              </div>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </div>
  </van-popup>

  <!-- COMBOBOX字段编辑弹窗 -->
  <van-popup 
    v-model:show="showComboBoxPopup" 
    position="bottom" 
    :style="{ width: '100%', height: 'auto' }" 
    safe-area-inset-bottom
    round>
    <div class="popup-container">
      <van-nav-bar
        :title="currentComboBoxField?.label"
        left-text="取消"
        right-text="确认"
        @click-left="showComboBoxPopup = false"
        @click-right="saveComboBoxValue"
      />
      <div class="popup-content">
        <van-radio-group v-model="selectedComboBoxValue" class="radio-group">
          <van-cell-group>
            <van-cell 
              v-for="(option, index) in ComboBoxOptions" 
              :key="index" 
              clickable 
              @click="selectedComboBoxValue = option.value">
              <div class="radio-cell">
                <van-radio :name="option.value" />
                <span class="radio-label">{{ option.label }}</span>
              </div>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </div>
  </van-popup>

  <!-- AMOUNT字段编辑弹窗 -->
  <van-popup 
    v-model:show="showAmountPopup" 
    position="bottom" 
    :style="{ width: '100%', height: 'auto' }" 
    safe-area-inset-bottom
    round>
    <div class="popup-container">
      <van-nav-bar
        :title="currentAmountField?.label"
        left-text="取消"
        right-text="确认"
        @click-left="showAmountPopup = false"
        @click-right="saveAmountValue"
      />
      <div class="popup-content">
        <div class="amount-input-container">
          <div class="amount-display">
            <span class="currency-symbol">¥</span>
            <span class="amount-value-display">{{ amountValue || '0.00' }}</span>
          </div>
          <van-number-keyboard
            v-model="amountValue"
            :show="showAmountPopup"
            theme="custom"
            extra-key="."
            :decimal-length="2"
            :maxlength="10"
            close-button-text="完成"
            @blur="showAmountPopup = false"
            @input="onAmountInput"
            @delete="onAmountDelete"
            @close="showAmountPopup = false"
          />
        </div>
      </div>
    </div>
  </van-popup>

  <!-- Owner选择弹窗 -->
  <van-popup 
    v-model:show="showOwnerPopup" 
    position="bottom" 
    :style="{ width: '100%', height: 'auto' }" 
    safe-area-inset-bottom
    round>
    <div class="popup-container">
      <van-nav-bar
        title="选择负责人"
        left-text="取消"
        right-text="确认"
        @click-left="showOwnerPopup = false"
        @click-right="saveOwnerValue"
      />
      <div class="popup-content">
        <van-radio-group v-model="selectedOwnerValue" class="radio-group">
          <van-cell-group>
            <van-cell 
              v-for="(option, index) in ownerOptions" 
              :key="index" 
              clickable 
              @click="selectedOwnerValue = option.value">
              <div class="owner-radio-cell">
                <van-radio :name="option.value" />
                <img :src="option.avatar" :alt="option.label" class="owner-option-avatar" />
                <span class="owner-radio-label">{{ option.label }}</span>
              </div>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </div>
  </van-popup>

  <!-- Status选择弹窗 -->
  <van-popup 
    v-model:show="showStatusPopup" 
    position="bottom" 
    :style="{ width: '100%', height: 'auto' }" 
    safe-area-inset-bottom
    round>
    <div class="popup-container">
      <van-nav-bar
        title="选择流程状态"
        left-text="取消"
        right-text="确认"
        @click-left="showStatusPopup = false"
        @click-right="saveStatusValue"
      />
      <div class="popup-content">
        <van-radio-group v-model="selectedStatusValue" class="radio-group">
          <van-cell-group>
            <van-cell 
              v-for="(option, index) in statusOptions" 
              :key="index" 
              clickable 
              @click="selectedStatusValue = option.value">
              <div class="status-radio-cell">
                <van-radio :name="option.value" />
                <span class="status-badge-popup" :class="`status-${option.status}`">{{ option.statusText }}</span>
                <span class="status-radio-label">——————> {{ option.label }}</span>
                
              </div>
            </van-cell>
          </van-cell-group>
        </van-radio-group>
      </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { FieldTypeEnum } from '@/enums/STCommonEnum'
import ItemHistory from '@/components/ItemHistory.vue'

// 定义字段类型
interface FieldValue {
  [key: string]: any
}

interface WorkOrderField {
  id: number
  label: string
  type: number
  value: any
  description: string
}

const route = useRoute()
const itemId = route.query.itemId as string
const projectId = route.query.projectId as string
const typeId = route.query.typeId as string

const activeNames = ref(['progress'])
const fieldListActiveNames = ref(['fieldList'])
const showScrollbar = ref(false)
let scrollTimeout: number | null = null

// PLAIN_TEXT弹窗相关数据
const showPlainTextPopup = ref(false)
const currentPlainTextfield = ref<WorkOrderField | null>(null)
const plainTextValue = ref('')

// DROPDOWN_LIST弹窗相关数据
const showDropdownPopup = ref(false)
const currentDropdownField = ref<WorkOrderField | null>(null)
const selectedDropdownValue = ref<string>('')
// 模拟下拉选项数据
const dropdownOptions = ref([
  { label: 'IT支持', value: 'IT支持' },
  { label: '软件问题', value: '软件问题' },
  { label: '硬件问题', value: '硬件问题' },
  { label: '网络问题', value: '网络问题' },
  { label: '系统问题', value: '系统问题' },
  { label: '其他', value: '其他' }
])

// MULTIPLE_SELECTION_LISTBOX弹窗相关数据
const showMultipleSelectionPopup = ref(false)
const currentMultipleSelectionField = ref<WorkOrderField | null>(null)
const selectedMultipleValues = ref<string[]>([])
// 模拟多选选项数据
const multipleSelectionOptions = ref([
  { label: '网络问题', value: '网络问题' },
  { label: '紧急', value: '紧急' },
  { label: '现场处理', value: '现场处理' },
  { label: '硬件故障', value: '硬件故障' },
  { label: '软件问题', value: '软件问题' },
  { label: '系统升级', value: '系统升级' },
  { label: '安全问题', value: '安全问题' }
])

// MULTILINE_EDIT_BOX弹窗相关数据
const showMultilineEditBoxPopup = ref(false)
const currentMultilineEditBoxField = ref<WorkOrderField | null>(null)
const multilineEditBoxValue = ref('')

// CHECKBOX弹窗相关数据
const showCheckboxPopup = ref(false)
const currentCheckboxField = ref<WorkOrderField | null>(null)
const selectedCheckboxValue = ref<string>('')
// CHECKBOX选项数据
const checkboxOptions = ref([
  { label: '是', value: '是' },
  { label: '否', value: '否' }
])

// RADIO_BUTTON弹窗相关数据
const showRadioPopup = ref(false)
const currentRadioField = ref<WorkOrderField | null>(null)
const selectedRadioValue = ref<string>('')
// RADIO_BUTTON选项数据
const RadioOptions = ref([
  { label: '严重', value: '严重' },
  { label: '高', value: '高' },
  { label: '中', value: '中' },
  { label: '低', value: '低' }
])

// COMBOBOX弹窗相关数据
const showComboBoxPopup = ref(false)
const currentComboBoxField = ref<WorkOrderField | null>(null)
const selectedComboBoxValue = ref<string>('')
// COMBOBOX选项数据
const ComboBoxOptions = ref([
  { label: '研发部', value: '研发部' },
  { label: '技术部', value: '技术部' },
  { label: '销售部', value: '销售部' },
  { label: '市场部', value: '市场部' }
])

// AMOUNT弹窗相关数据
const showAmountPopup = ref(false)
const currentAmountField = ref<WorkOrderField | null>(null)
const amountValue = ref<string>('')

// Owner弹窗相关数据
const showOwnerPopup = ref(false)
const selectedOwnerValue = ref<string>('zhangsan') // 默认选中周佳杰
// 模拟人员列表数据
const ownerOptions = ref([
  { 
    label: '周佳杰', 
    value: 'zhangsan',
    avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80'
  },
  { 
    label: '陈媛', 
    value: 'lisi',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80'
  },
  { 
    label: '王并才', 
    value: 'wangwu',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80'
  },
  { 
    label: '卜佳伟', 
    value: 'zhaoliu',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80'
  }
])

// Status弹窗相关数据
const showStatusPopup = ref(false)
const selectedStatusValue = ref<string>('processing')
// 模拟流程状态数据
const statusOptions = ref([
  { 
    label: '待处理', 
    value: 'pending',
    status: 'pending',
    statusText: '待处理'
  },
  { 
    label: '处理中', 
    value: 'processing',
    status: 'processing',
    statusText: '处理中'
  },
  { 
    label: '已完成', 
    value: 'completed',
    status: 'completed',
    statusText: '已完成'
  },
  { 
    label: '已关闭', 
    value: 'closed',
    status: 'closed',
    statusText: '已关闭'
  },
  { 
    label: '已取消', 
    value: 'cancelled',
    status: 'cancelled',
    statusText: '已取消'
  }
])


// 可编辑字段相关数据
const editableFieldId = ref<number | null>(null)
const fieldRefs = ref<Record<number, HTMLElement>>({})

// 编辑字段相关方法
const editField = async (field: WorkOrderField, event: any) => {
  event.stopPropagation();
  // 只有SHORT_TEXT类型的字段可以编辑
  if (field.type === FieldTypeEnum.SHORT_TEXT) {
    editableFieldId.value = field.id
    
    // 等待 DOM 更新后聚焦
    await nextTick()
    
    // 查找并聚焦到输入框
    const input = document.querySelector(`[data-field-id="${field.id}"] input`) as HTMLInputElement | null
      if (input) {
        input.focus()
      }
  }
  // PLAIN_TEXT类型字段弹出popup编辑
  else if (field.type === FieldTypeEnum.PLAIN_TEXT) {
    currentPlainTextfield.value = field
    plainTextValue.value = field.value || ''
    showPlainTextPopup.value = true
  }
  // DROPDOWN_LIST类型字段弹出popup编辑
  else if (field.type === FieldTypeEnum.DROPDOWN_LIST) {
    currentDropdownField.value = field
    selectedDropdownValue.value = field.value || ''
    showDropdownPopup.value = true
  }
  // MULTIPLE_SELECTION_LISTBOX类型字段弹出popup编辑
  else if (field.type === FieldTypeEnum.MULTIPLE_SELECTION_LISTBOX) {
    currentMultipleSelectionField.value = field
    // 初始化选中值
    selectedMultipleValues.value = Array.isArray(field.value) ? [...field.value] : []
    showMultipleSelectionPopup.value = true
  }
  // MULTILINE_EDIT_BOX类型字段弹出popup编辑
  else if (field.type === FieldTypeEnum.MULTILINE_EDIT_BOX) {
    currentMultilineEditBoxField.value = field
    multilineEditBoxValue.value = field.value || ''
    showMultilineEditBoxPopup.value = true
  }
  // CHECKBOX类型字段弹出popup编辑
  else if (field.type === FieldTypeEnum.CHECKBOX) {
    currentCheckboxField.value = field
    selectedCheckboxValue.value = field.value || ''
    showCheckboxPopup.value = true
  }
  // RADIO_BUTTON类型字段弹出popup编辑
  else if (field.type === FieldTypeEnum.RADIO_BUTTON) {
    currentRadioField.value = field
    selectedRadioValue.value = field.value || ''
    showRadioPopup.value = true
  }
   // COMBOBOX类型字段弹出popup编辑
  else if (field.type === FieldTypeEnum.COMBOBOX) {
    currentComboBoxField.value = field
    selectedComboBoxValue.value = field.value || ''
    showComboBoxPopup.value = true
  }
  // AMOUNT类型字段弹出popup编辑
  else if (field.type === FieldTypeEnum.AMOUNT) {
    currentAmountField.value = field
    amountValue.value = field.value || ''
    showAmountPopup.value = true
  }
}

const saveField = async (field: WorkOrderField) => {
  // 保存字段值，这里可以添加实际的保存逻辑
  console.log('保存字段:', field)
  editableFieldId.value = null
  
  // 等待界面更新
  await nextTick()
}

// 保存PLAIN_TEXT字段值
const savePlainTextValue = () => {
  if (currentPlainTextfield.value) {
    currentPlainTextfield.value.value = plainTextValue.value
    saveField(currentPlainTextfield.value)
  }
  showPlainTextPopup.value = false
}

// 保存DROPDOWN_LIST字段值
const saveDropdownValue = () => {
  if (currentDropdownField.value) {
    currentDropdownField.value.value = selectedDropdownValue.value
    saveField(currentDropdownField.value)
  }
  showDropdownPopup.value = false
}

// 保存MULTIPLE_SELECTION_LISTBOX字段值
const saveMultipleSelectionValue = () => {
  if (currentMultipleSelectionField.value) {
    currentMultipleSelectionField.value.value = [...selectedMultipleValues.value]
    saveField(currentMultipleSelectionField.value)
  }
  showMultipleSelectionPopup.value = false
}

// 保存MULTILINE_EDIT_BOX字段值
const saveMultilineEditBoxValue = () => {
  if (currentMultilineEditBoxField.value) {
    currentMultilineEditBoxField.value.value = multilineEditBoxValue.value
    saveField(currentMultilineEditBoxField.value)
  }
  showMultilineEditBoxPopup.value = false
}

// 保存CHECKBOX字段值
const saveCheckboxValue = () => {
  if (currentCheckboxField.value) {
    currentCheckboxField.value.value = selectedCheckboxValue.value
    saveField(currentCheckboxField.value)
  }
  showCheckboxPopup.value = false
}

// 保存RADIO_BUTTON字段值
const saveRadioValue = () => {
  if (currentRadioField.value) {
    currentRadioField.value.value = selectedRadioValue.value
    saveField(currentRadioField.value)
  }
  showRadioPopup.value = false
}

// 保存COMBOBOX字段值
const saveComboBoxValue = () => {
  if (currentComboBoxField.value) {
    currentComboBoxField.value.value = selectedComboBoxValue.value
    saveField(currentComboBoxField.value)
  }
  showComboBoxPopup.value = false
}

// 保存AMOUNT字段值
const saveAmountValue = () => {
  if (currentAmountField.value) {
    currentAmountField.value.value = amountValue.value
    saveField(currentAmountField.value)
  }
  showAmountPopup.value = false
}

// 保存Owner值
const saveOwnerValue = () => {
  const selectedOwner = ownerOptions.value.find(option => option.value === selectedOwnerValue.value)
  if (selectedOwner) {
    workOrderData.value.owner = {
      name: selectedOwner.label,
      avatar: selectedOwner.avatar
    }
  }
  showOwnerPopup.value = false
}

// 保存Status值
const saveStatusValue = () => {
  const selectedStatus = statusOptions.value.find(option => option.value === selectedStatusValue.value)
  if (selectedStatus) {
    workOrderData.value.status = selectedStatus.status
    workOrderData.value.statusText = selectedStatus.statusText
  }
  showStatusPopup.value = false
}

// AMOUNT输入处理
const onAmountInput = (value: string) => {
  amountValue.value = value
}

// AMOUNT删除处理
const onAmountDelete = () => {
  if (amountValue.value.length > 0) {
    amountValue.value = amountValue.value.slice(0, -1)
  }
}

// 滚动处理函数
const handleScroll = () => {
  showScrollbar.value = true
  
  // 清除之前的定时器
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
  
  // 1秒后隐藏滚动条
  scrollTimeout = setTimeout(() => {
    showScrollbar.value = false
  }, 1000)
}

// 触摸滚动处理函数（移动端优化）
const handleTouchStart = () => {
  showScrollbar.value = true
}

const handleTouchEnd = () => {
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
  scrollTimeout = setTimeout(() => {
    showScrollbar.value = false
  }, 1000)
}

// 组件挂载时添加滚动监听
onMounted(() => {
  const container = document.querySelector('.page-container')
  if (container) {
    container.addEventListener('scroll', handleScroll)
    container.addEventListener('touchstart', handleTouchStart)
    container.addEventListener('touchend', handleTouchEnd)
  }
})

// 组件卸载时移除滚动监听
onUnmounted(() => {
  const container = document.querySelector('.page-container')
  if (container) {
    container.removeEventListener('scroll', handleScroll)
    container.removeEventListener('touchstart', handleTouchStart)
    container.removeEventListener('touchend', handleTouchEnd)
  }
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
})

// 工单基本信息数据
const workOrderData = ref({
  title: 'OA流程：关于《主数据管理办法》暂停修订的请示 没有SN 不给归档',
  number: 'WO-2023042501',
  status: 'processing',
  statusText: '处理中',
  owner: {
    name: '周佳杰',
    avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80'
  }
})

// 字段定义和演示数据 - 包含所有STCommonEnum.ts中的字段类型
const workOrderFields = ref([
  // 单行输入框 (SHORT_TEXT = 1)
  {
    id: 1,
    label: '工单标题',
    type: FieldTypeEnum.SHORT_TEXT,
    value: '网络连接问题',
    description: '工单标题'
  },
  // 多行输入框 (PLAIN_TEXT = 2)
  {
    id: 2,
    label: '详细描述',
    type: FieldTypeEnum.PLAIN_TEXT,
    value: '无法连接到公司内网，已经重启路由器但问题仍然存在。尝试了多次连接，每次都会提示"无法连接到网络"。其他同事的电脑可以正常连接，只有我的电脑不行。',
    description: '工单详细描述'
  },
  // 单选框 (DROPDOWN_LIST = 3)
  {
    id: 3,
    label: '工单分类',
    type: FieldTypeEnum.DROPDOWN_LIST,
    value: 'IT支持',
    description: '工单所属分类'
  },
  // 多选框 (MULTIPLE_SELECTION_LISTBOX = 4)
  {
    id: 4,
    label: '标签',
    type: FieldTypeEnum.MULTIPLE_SELECTION_LISTBOX,
    value: ['网络问题', '紧急', '现场处理'],
    description: '工单标签'
  },
  // 日期选择框 (DATE_TIME_FIELD = 5)
  {
    id: 5,
    label: '创建时间',
    type: FieldTypeEnum.DATE_TIME_FIELD,
    value: '2023-04-25 14:30',
    description: '工单创建时间'
  },
  // 多行输入框,富文本输入框 (MULTILINE_EDIT_BOX = 6)
  {
    id: 6,
    label: '解决方案',
    type: FieldTypeEnum.MULTILINE_EDIT_BOX,
    value: null,
    description: '问题解决方案'
  },
  // 复选框 (CHECKBOX = 7)
  {
    id: 7,
    label: '是否紧急',
    type: FieldTypeEnum.CHECKBOX,
    value: '是',
    description: '是否为紧急工单'
  },
  // 单选框 (RADIO_BUTTON = 8)
  {
    id: 8,
    label: '严重程度',
    type: FieldTypeEnum.RADIO_BUTTON,
    value: '高',
    description: '问题严重程度'
  },
  // 静态文本 (STATIC_LABEL = 9)
  {
    id: 9,
    label: '备注',
    type: FieldTypeEnum.STATIC_LABEL,
    value: '额外备注信息',
    description: '额外备注信息'
  },
  // 组合框 (COMBOBOX = 10)
  {
    id: 10,
    label: '所属部门',
    type: FieldTypeEnum.COMBOBOX,
    value: '技术部',
    description: '工单所属部门'
  },
  // 金额 (AMOUNT = 11)
  {
    id: 11,
    label: '处理费用',
    type: FieldTypeEnum.AMOUNT,
    value: '',
    description: '工单处理费用'
  },
  // 用户类型字段
  {
    id: 12,
    label: '申请人',
    type: 0, // 特殊类型，不在STCommonEnum中
    value: {
      name: '周佳杰',
      avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80'
    },
    description: '工单申请人信息'
  },
  {
    id: 13,
    label: '处理人',
    type: 0, // 特殊类型，不在STCommonEnum中
    value: {
      name: 'IT支持 - 王明',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80'
    },
    description: '工单处理人信息'
  },
  // 优先级字段
  {
    id: 16,
    label: '优先级',
    type: FieldTypeEnum.DROPDOWN_LIST, // 特殊类型
    value: "高",
    description: '工单优先级'
  },
  // 附件字段
  {
    id: 17,
    label: '附件',
    type: 17, // 特殊类型
    value: [
      {
        name: '错误截图.png',
        size: '1.2MB'
      }
    ],
    description: '工单相关附件'
  },
  // 位置信息
  {
    id: 14,
    label: '问题位置',
    type: FieldTypeEnum.SHORT_TEXT,
    value: '3楼办公区A区',
    description: '问题发生位置'
  },
  // 设备信息
  {
    id: 15,
    label: '设备信息',
    type: FieldTypeEnum.PLAIN_TEXT,
    value: 'ThinkPad T480, IP: ************',
    description: '相关设备信息'
  }
])

// 类型检查辅助函数
const isUserField = (field: WorkOrderField) => {
  return field.type === 0 && field.value && typeof field.value === 'object' && 'avatar' in field.value && 'name' in field.value
}

const isAttachmentField = (field: WorkOrderField) => {
  return field.type === 17 && Array.isArray(field.value) && field.value.length > 0 && field.value[0] && typeof field.value[0] === 'object' && 'name' in field.value[0]
}

</script>

<style scoped>
.page-container {
  padding-top: 46px;
  padding-bottom: 50px;
  background-color: #f5f7fa;
  min-height: 100vh;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  -webkit-overflow-scrolling: touch; /* 移动端平滑滚动 */
  position: relative;
  height: 100vh;
  box-sizing: border-box;
}

/* 弹窗全屏布局样式 */
.full-height {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
  overflow: hidden;
}

.full-height-field {
  height: 100%;
}

.full-height-field :deep(.van-field__control) {
  height: 100%;
}

.page-container::-webkit-scrollbar {
  width: 8px;
  background-color: transparent;
}

.page-container::-webkit-scrollbar-track {
  background-color: transparent;
}

.page-container::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.page-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.5);
}

.page-container.show-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.4) !important;
}

/* 确保滚动条在显示时有足够的对比度 */
.page-container.show-scrollbar::-webkit-scrollbar {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 移动端优化 */
@media (max-width: 768px) {
  .page-container {
    padding-bottom: 60px; /* 为底部导航栏留出更多空间 */
  }
  
  .page-container::-webkit-scrollbar {
    width: 6px; /* 移动端滚动条更细 */
  }
  
  .page-container.show-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.4) !important;
  }
}
.detail-header {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  margin: 15px;
  padding: 20px;
  overflow: hidden;
}

.detail-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  margin: 15px;
  padding: 5px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 5px;
}

.title-section {
  flex: 1;
}

.title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.work-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin: 5px 0;
  flex: 1;
}

.owner-avatar {
  margin-left: 12px;
  cursor: pointer;
}

.owner-avatar img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.owner-avatar:hover img {
  transform: scale(1.05);
}

.owner-radio-cell {
  display: flex;
  align-items: center;
  width: 100%;
}

.owner-option-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-left: 12px;
  margin-right: 12px;
}

.owner-radio-label {
  font-size: 14px;
  color: #333;
}

.status-radio-cell {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  text-align: left;
}

.status-radio-label {
  font-size: 13px;
  color: #333;
  flex: 1;
  margin-left: 12px;
}

.status-badge-popup {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  margin-left: 8px;
}

.status-row {
  margin-top: 4px;
}

.work-number {
  font-size: 14px;
  color: #999;
}

.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.status-badge:hover {
  transform: scale(1.05);
}

.status-processing {
  background-color: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.status-completed {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.status-pending {
  background-color: rgba(144, 147, 153, 0.1);
  color: #909399;
}

.status-closed {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.status-cancelled {
  background-color: rgba(144, 147, 153, 0.1);
  color: #909399;
}

/* 字段样式 */
.field-label {
  color: #999;
  font-size: 14px;
}

.field-value {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.field-value-vertical {
  width: 100%;
}

.field-value.editable-field {
  justify-content: flex-start; /* 确保编辑状态也左对齐 */
  padding: 0;
  margin: 0;
}

.field-value-left {
  width: 100%;
  text-align: left;
}

.normal-value {
  color: #333;
  font-size: 14px;
  text-align: left;
}

.empty-value {
  color: #ccc;
  font-size: 14px;
  text-align: left;
}

/* 可编辑字段样式 */
.editable-field {
  padding: 6px 0;
  font-size: 14px;
  color: #333;
  width: 100%;
}

.editable-field :deep(.van-field__control) {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  text-align: left;
  padding: 6px 0;
}

.editable-field-container {
  width: 100%;
  padding: 6px 0;
  line-height: 1.5;
  box-sizing: border-box;
  text-align: left;
}

/* 用户信息样式 */
.user-info {
  display: flex;
  align-items: center;
}

.avatar-small {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 8px;
}

/* 优先级样式 */
.priority-info {
  display: flex;
  align-items: center;
}

.priority-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.priority-high {
  background-color: #F56C6C;
}

.priority-medium {
  background-color: #E6A23C;
}

.priority-low {
  background-color: #67C23A;
}

/* 标签样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag {
  background-color: #f0f2f5;
  color: #666;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

/* 复选框样式 */
/* .checked {
  color: #F56C6C;
  font-weight: 500;
} */

.unchecked {
  color: #ccc;
}

/* 金额样式 */
.amount-value {
  color: #F56C6C;
  font-weight: 500;
}

/* 附件样式 */
.attachment-item {
  display: flex;
  align-items: center;
  background: #f9fafc;
  padding: 8px;
  border-radius: 6px;
  margin-top: 4px;
}

.attachment-icon {
  color: #409EFF;
  font-size: 16px;
  margin-right: 8px;
}

.attachment-info {
  flex: 1;
}

.attachment-name {
  font-size: 12px;
  font-weight: 500;
  color: #333;
}

.attachment-size {
  font-size: 10px;
  color: #999;
  margin-top: 2px;
}

.attachment-download {
  color: #999;
  font-size: 14px;
}

.progress-card {
  margin: 0 15px 15px 15px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
}

.timeline {
  position: relative;
  padding-left: 25px;
}

.timeline:before {
  content: '';
  position: absolute;
  left: 5px;
  top: 10px;
  bottom: 0;
  width: 2px;
  background-color: #e0e0e0;
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-item:before {
  content: '';
  position: absolute;
  left: -25px;
  top: 4px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #409EFF;
  border: 2px solid #fff;
  z-index: 1;
}

.timeline-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.timeline-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.timeline-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.vertical-cell {
  flex-direction: column;
  align-items: flex-start !important;
}

.vertical-cell :deep(.van-cell__title) {
  margin-bottom: 8px;
  width: 100%;
  color:#999;
}

.vertical-cell :deep(.van-cell__value) {
  width: 100%;
  text-align: left;
  margin-left: 0;
}

.consistent-height {
  min-height: 32px; /* 与 van-field 高度保持一致 */
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

/* PLAIN_TEXT弹窗样式 */
.popup-container {
  width: 100vw;
  background-color: #fff;
}

.popup-content {
  padding: 16px;
}

.popup-content :deep(.van-field__word-limit) {
  text-align: right;
  margin-top: 4px;
}

.radio-group {
  width: 100%;
}

.radio-cell {
  display: flex;
  align-items: center;
  width: 100%;
}

.radio-label {
  margin-left: 12px;
  font-size: 14px;
  color: #333;
}

.checkbox-group {
  width: 100%;
}

.checkbox-cell {
  display: flex;
  align-items: center;
  width: 100%;
}

.checkbox-label {
  margin-left: 12px;
  font-size: 14px;
  color: #333;
}

/* AMOUNT弹窗样式 */
.amount-input-container {
  width: 100%;
}

.amount-display {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.currency-symbol {
  font-size: 24px;
  font-weight: 500;
  color: #333;
  margin-right: 8px;
}

.amount-value-display {
  font-size: 24px;
  font-weight: 500;
  color: #333;
  min-width: 100px;
  text-align: left;
}

.amount-value {
  color: #F56C6C;
  font-weight: 500;
}

.status-owner-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-container {
  flex: 1;
}

.owner-container {
  flex-shrink: 0;
  margin-left: 12px;
}



</style>
