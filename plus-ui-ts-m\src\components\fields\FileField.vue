<template>
  <div class="file-field">
    <div
      v-for="(file, index) in files"
      :key="index"
      class="file-item"
      @click="downloadFile(file)"
    >
      <van-icon name="certificate" />
      <span class="file-name">{{ file.name }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { PageFieldVo } from '@/api/field/types'

interface Props {
  field: PageFieldVo
  fieldValue: any
}

const props = defineProps<Props>()

const files = computed(() => {
  if (Array.isArray(props.fieldValue)) {
    return props.fieldValue
  }
  return []
})

const downloadFile = (file: any) => {
  // 实现文件下载逻辑
  console.log('下载文件:', file)
}
</script>

<style scoped>
.file-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
}

.file-name {
  margin-left: 8px;
  color: #1989fa;
}
</style>