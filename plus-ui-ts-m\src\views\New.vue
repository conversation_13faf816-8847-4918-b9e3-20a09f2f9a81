<template>
  <div class="page-container">
    <!-- 顶部栏 -->
    <van-nav-bar 
      title="创建工单" 
      left-text="取消" 
      @click-left="$router.back()" 
      right-text="提交"
      @click-right="onSubmit"
      fixed />

    <div class="content-container" @scroll="handleScroll" ref="contentContainer">
      <!-- 新建工单表单 -->
      <div class="form-container">
        <form @submit="onSubmit" class="work-order-form">
          <!-- 客户信息 -->
          <div class="form-group">
            <label class="form-label">客户 <span class="required">*</span></label>
            <div class="select-wrapper">
              <van-field
                v-model="form.customer"
                placeholder="请选择客户"
                readonly
                @click="showCustomer = true"
                class="form-control"
              />
              <van-popup v-model:show="showCustomer" round position="bottom">
                <van-picker
                  :columns="customerOptions"
                  @confirm="onCustomerConfirm"
                  @cancel="showCustomer = false"
                />
              </van-popup>
            </div>
          </div>

          <!-- 工单类型 -->
          <div class="form-group">
            <label class="form-label">工单类型 <span class="required">*</span></label>
            <div class="select-wrapper">
              <van-field
                v-model="form.type"
                placeholder="请选择工单类型"
                readonly
                @click="showType = true"
                class="form-control"
              />
              <van-popup v-model:show="showType" round position="bottom">
                <van-picker
                  :columns="typeOptions"
                  @confirm="onTypeConfirm"
                  @cancel="showType = false"
                />
              </van-popup>
            </div>
          </div>

          <!-- 工单标题 -->
          <div class="form-group">
            <label class="form-label">工单标题 <span class="required">*</span></label>
            <van-field
              v-model="form.title"
              placeholder="请输入工单标题，简要描述问题"
              class="form-control"
            />
          </div>

          <!-- 问题描述 -->
          <div class="form-group">
            <label class="form-label">问题描述 <span class="required">*</span></label>
            <van-field
              v-model="form.description"
              type="textarea"
              placeholder="请详细描述您遇到的问题，包括症状、影响、发生时间等信息"
              rows="4"
              class="form-control textarea-control"
            />
          </div>

          <!-- 优先级 -->
          <div class="form-group">
            <label class="form-label">优先级 <span class="required">*</span></label>
            <div class="select-wrapper">
              <van-field
                v-model="form.priority"
                placeholder="请选择优先级"
                readonly
                @click="showPriority = true"
                class="form-control"
              />
              <van-popup v-model:show="showPriority" round position="bottom">
                <van-picker
                  :columns="priorityOptions"
                  @confirm="onPriorityConfirm"
                  @cancel="showPriority = false"
                />
              </van-popup>
            </div>
          </div>

          <!-- 上传附件 -->
          <div class="form-group">
            <label class="form-label">上传附件</label>
            <div class="file-upload-area">
              <van-uploader 
                v-model="fileList" 
                multiple 
                :max-count="3"
                class="custom-uploader"
                :after-read="afterRead"
                :before-delete="beforeDelete"
              />
            </div>
          </div>

          <!-- 联系方式 -->
          <div class="form-group">
            <label class="form-label">联系方式</label>
            <van-field
              v-model="form.contact"
              placeholder="请输入您的联系方式，如电话号码"
              class="form-control"
            />
          </div>

        </form>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <van-tabbar route fixed>
      <van-tabbar-item to="/home" icon="home-o">首页</van-tabbar-item>
      <van-tabbar-item to="/projects" icon="apps-o">项目</van-tabbar-item>
      <van-tabbar-item to="/reports" icon="chart-trending-o">报表</van-tabbar-item>
      <van-tabbar-item to="/profile" icon="user-o">我的</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { showToast } from 'vant'

const form = ref({
  customer: '',
  type: '',
  title: '',
  description: '',
  priority: '',
  contact: ''
})
const fileList = ref([])
const showCustomer = ref(false)
const showType = ref(false)
const showPriority = ref(false)
const customerOptions = [
  { text: '客户A', value: '客户A' },
  { text: '客户B', value: '客户B' },
  { text: '客户C', value: '客户C' },
  { text: '客户D', value: '客户D' },
  { text: '客户E', value: '客户E' }
]
const typeOptions = [
  { text: '网络问题', value: '网络问题' },
  { text: '软件安装', value: '软件安装' },
  { text: '硬件故障', value: '硬件故障' },
  { text: '账户权限', value: '账户权限' },
  { text: '其他', value: '其他' }
]
const priorityOptions = [
  { text: '低 - 不急需解决', value: '低' },
  { text: '中 - 影响工作但有替代方案', value: '中' },
  { text: '高 - 严重影响工作效率', value: '高' },
  { text: '紧急 - 无法工作', value: '紧急' }
]

const contentContainer = ref<HTMLDivElement | null>(null)
const scrollTimer = ref<number | null>(null)

function onCustomerConfirm(val: string) {
  form.value.customer = val
  showCustomer.value = false
}
function onTypeConfirm(val: string) {
  form.value.type = val
  showType.value = false
}
function onPriorityConfirm(val: string) {
  form.value.priority = val
  showPriority.value = false
}
function afterRead(file: any) {
  showToast('文件上传成功')
}
function beforeDelete(file: any) {
  return true
}
function onSubmit() {
  showToast('工单创建成功！')
  setTimeout(() => {
    window.location.href = '/list'
  }, 800)
}

// 滚动事件处理函数
const handleScroll = () => {
  if (contentContainer.value) {
    // 添加滚动时的类
    contentContainer.value.classList.add('scrolling');
    
    // 清除之前的定时器
    if (scrollTimer.value) {
      clearTimeout(scrollTimer.value);
    }
    
    // 设置新的定时器，1秒后隐藏滚动条
    scrollTimer.value = window.setTimeout(() => {
      if (contentContainer.value) {
        contentContainer.value.classList.remove('scrolling');
      }
      scrollTimer.value = null;
    }, 1000);
  }
}
</script>

<style scoped>
.page-container {
  padding-top: 46px;
  padding-bottom: 50px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.content-container {
  height: calc(100vh - 46px - 50px);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  padding: 15px;
  padding-bottom: 70px; /* 为底部固定元素留出空间 */
}

.content-container::-webkit-scrollbar {
  width: 6px;
}

.content-container::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 3px;
}

.content-container.scrolling::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  transition: background-color 0.3s;
}

.content-container.scrolling {
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
}

.work-order-form {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  overflow: hidden;
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.required {
  color: #F56C6C;
}

.form-control {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 12px 15px;
  font-size: 14px;
  transition: all 0.3s;
  background: #fff;
}

.form-control:focus {
  border-color: #409EFF;
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.textarea-control {
  min-height: 120px;
  resize: vertical;
}

.select-wrapper {
  position: relative;
}

.select-wrapper:after {
  content: '';
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #666;
  pointer-events: none;
  z-index: 1;
}

.file-upload-area {
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  padding: 20px;
  background-color: #f0f2f5;
  text-align: center;
  transition: all 0.3s;
}

.file-upload-area:hover {
  border-color: #409EFF;
  color: #409EFF;
}

.custom-uploader {
  width: 100%;
}

.custom-uploader :deep(.van-uploader__upload) {
  width: 100%;
  height: 80px;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  background-color: #f0f2f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 14px;
  transition: all 0.3s;
}

.custom-uploader :deep(.van-uploader__upload:hover) {
  border-color: #409EFF;
  color: #409EFF;
}

.custom-uploader :deep(.van-uploader__preview) {
  margin: 8px 8px 0 0;
}

.custom-uploader :deep(.van-uploader__preview-image) {
  width: 80px;
  height: 80px;
  border-radius: 8px;
}

.submit-section {
  margin-top: 30px;
  margin-bottom: 20px;
}

.submit-btn {
  height: 44px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 16px;
  background-color: #409EFF;
  border-color: #409EFF;
}

/* 覆盖 Vant 默认样式 */
:deep(.van-field__control) {
  border: none !important;
  background: transparent !important;
  padding: 0 !important;
}

:deep(.van-field__control:focus) {
  border: none !important;
  box-shadow: none !important;
}

:deep(.van-field__label) {
  display: none !important;
}

:deep(.van-field__value) {
  padding: 0 !important;
}

:deep(.van-field__body) {
  padding: 0 !important;
}
</style>