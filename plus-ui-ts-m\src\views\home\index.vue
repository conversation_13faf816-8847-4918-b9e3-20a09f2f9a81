<template>
    <div class="page-container">
      <!-- 顶部栏 -->
      <van-nav-bar title="ITSM系统首页" fixed />
  
      <div class="content-container" @scroll="handleScroll">
        <!-- 统计数字 -->
        <div class="stats-card">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-number  inprogress">3</div>
              <div class="stat-label">处理中工单</div>
            </div>
            <div class="stat-item">
              <div class="stat-number submitted">15</div>
              <div class="stat-label">已提交工单</div>
            </div>
            <div class="stat-item">
              <div class="stat-number completed">12</div>
              <div class="stat-label">已完成工单</div>
            </div>
          </div>
        </div>
  
        <!-- 项目菜单 -->
        <div class="projects-card">
          <div class="section-header">项目清单</div>
          <div v-if="loading" class="loading">
            <!-- <van-loading type="spinner" color="#409EFF" />
            <p>加载中...</p> -->
          </div>
          <div v-else-if="error" class="error">
            <p>{{ error }}</p>
            <van-button type="primary" size="small" @click="fetchprojects">重试</van-button>
          </div>
          <ProjectList v-else :projects="projects" />
        </div>
  
        <!-- 公告区 -->
        <AnnouncementList />
      </div>
  

  
      <!-- 底部导航栏 -->
      <van-tabbar route fixed>
        <van-tabbar-item to="/home" icon="home-o">首页</van-tabbar-item>
        <van-tabbar-item to="/projects" icon="apps-o">项目</van-tabbar-item>
        <van-tabbar-item to="/reports" icon="chart-trending-o">报表</van-tabbar-item>
        <van-tabbar-item to="/profile" icon="user-o">我的</van-tabbar-item>
      </van-tabbar>
    </div>
  </template>
  
  <script setup lang="ts">
  // vant 组件已全局引入，无需单独import
  import { ref, onMounted } from 'vue';
  import projectList from '@/components/ProjectList.vue'
  import AnnouncementList from '@/components/AnnouncementList.vue'
  import { getProjectListByUser } from '@/api/general/index'
  import type { ProjectListData } from '@/api/general/types'
  
  const scrollTimer = ref<number | null>(null);

  // 项目相关数据
  const projects = ref<ProjectListData[]>([])
  const loading = ref(true)
  const error = ref('')

  const fetchprojects = async () => {
    try {
      loading.value = true
      error.value = ''
      const response = await getProjectListByUser()
      
      // 获取children数据作为项目数据
      let allChildren: ProjectListData[] = []
      
      if (Array.isArray(response.data)) {
        // 如果response.data是数组，遍历数组中的每个对象，收集所有children
        response.data.forEach(item => {
          if (item.children && Array.isArray(item.children)) {
            allChildren = allChildren.concat(item.children)
          }
        })
      } else if (response.data && Array.isArray(response.data.children)) {
        // 如果response.data是对象且包含children数组
        allChildren = response.data.children
      }
      
      projects.value = allChildren
    } catch (err) {
      console.error('获取项目数据失败:', err)
      error.value = '获取项目数据失败，请重试'
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    fetchprojects()
  })
  
  const handleScroll = () => {
    const contentContainer = document.querySelector('.content-container') as HTMLElement;
    if (contentContainer) {
      // 添加滚动时的类
      contentContainer.classList.add('scrolling');
      
      // 清除之前的定时器
      if (scrollTimer.value) {
        clearTimeout(scrollTimer.value);
      }
      
      // 设置新的定时器，1秒后隐藏滚动条
      scrollTimer.value = window.setTimeout(() => {
        contentContainer.classList.remove('scrolling');
        scrollTimer.value = null;
      }, 1000);
    }
  };
  

  </script>
  
  <style scoped>
  .page-container {
    padding-top: 46px;
    padding-bottom: 50px;
    background-color: #f5f7fa;
    min-height: 100vh;
  }
  
  .content-container {
    height: calc(100vh - 46px - 50px);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: transparent transparent;
  }
  
  .content-container::-webkit-scrollbar {
    width: 6px;
  }
  
  .content-container::-webkit-scrollbar-thumb {
    background-color: transparent;
    border-radius: 3px;
  }
  
  .content-container.scrolling::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.3);
    transition: background-color 0.3s;
  }
  
  .content-container.scrolling {
    scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
  }
  
  .user-info-card {
    background-color: #fff;
    padding: 15px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;
    position: relative;
  }
  
  .user-avatar::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
      to bottom right,
      rgba(255, 255, 255, 0.3),
      rgba(255, 255, 255, 0)
    );
    transform: rotate(30deg);
  }
  
  .user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .user-details {
    flex: 1;
  }
  
  .user-name {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
  }
  
  .user-role {
    font-size: 14px;
    color: #999;
  }
  
  .projects-card {
    background-color: #fff;
    margin: 15px;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    overflow: hidden;
  }

  .loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #999;
  }

  .loading p {
    margin-top: 10px;
    font-size: 14px;
  }

  .error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #F56C6C;
  }

  .error p {
    margin-bottom: 15px;
    font-size: 14px;
  }
  
  .section-header {
    padding: 15px 15px 10px;
    font-weight: 500;
    font-size: 16px;
    color: #333;
  }
  
  .project-list {
    padding: 0 15px 15px;
  }
  

  
  .stats-card {
    background-color: #fff;
    margin: 15px 15px 15px 15px;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    overflow: hidden;
  }
  
  .stats-grid {
    display: flex;
    padding: 20px 0;
  }
  
  .stat-item {
    flex: 1;
    text-align: center;
    position: relative;
  }
  
  .stat-item:not(:last-child)::after {
    content: '';
    position: absolute;
    right: 0;
    top: 20%;
    bottom: 20%;
    width: 1px;
    background-color: #f0f0f0;
  }
  
  .stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #409EFF;
    margin-bottom: 8px;
  }
  
  .inprogress {
    color: #E6A23C; /* 橙色 - 处理中 */
  }
  
  .submitted {
    color: #409EFF; /* 蓝色 - 已提交 */
  }
  
  .completed {
    color: #67C23A; /* 绿色 - 已完成 */
  }
  
  .stat-label {
    font-size: 12px;
    color: #999;
  }
  

  </style>