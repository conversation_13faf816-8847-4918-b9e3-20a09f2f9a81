<template>
  <div class="knowledge-list" ref="scrollContainer">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div v-if="articles.length === 0 && !loading" class="empty-state">
          <van-icon name="bookmark-o" class="empty-state-icon" />
          <p>暂无知识文章</p>
        </div>
        
        <div v-else class="article-list">
          <div
            v-for="article in articles"
            :key="article.id"
            class="article-item card"
            @click="viewArticle(article)"
          >
            <div class="article-header">
              <div class="article-title">{{ article.title }}</div>
              <van-tag :type="getCategoryType(article.category)">
                {{ getCategoryText(article.category) }}
              </van-tag>
            </div>
            
            <div class="article-content">
              <div class="article-desc">{{ article.description }}</div>
              <div class="article-meta">
                <div class="meta-item">
                  <van-icon name="clock-o" />
                  <span>{{ article.createTime }}</span>
                </div>
                <div class="meta-item">
                  <van-icon name="eye-o" />
                  <span>{{ article.views }}次阅读</span>
                </div>
                <div class="meta-item">
                  <van-icon name="like-o" />
                  <span>{{ article.likes }}次点赞</span>
                </div>
              </div>
            </div>
            
            <div class="article-footer">
              <van-button 
                type="primary" 
                size="small"
                plain
                @click.stop="likeArticle(article.id)"
              >
                <van-icon name="like-o" />
                有用
              </van-button>
              <van-button 
                type="default" 
                size="small"
                plain
                @click.stop="shareArticle(article)"
              >
                <van-icon name="share-o" />
                分享
              </van-button>
            </div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

interface Article {
  id: string
  title: string
  description: string
  content: string
  category: string
  createTime: string
  views: number
  likes: number
  author: string
}

const props = defineProps<{
  category: string
  search: string
}>()

const router = useRouter()
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const articles = ref<Article[]>([])
const scrollContainer = ref<HTMLElement | null>(null)
const scrollTimer = ref<number | null>(null)

// 模拟数据
const mockArticles: Article[] = [
  {
    id: '1',
    title: '如何解决电脑无法开机问题',
    description: '详细介绍电脑无法开机的常见原因和解决方法，包括硬件检查、系统修复等步骤。',
    content: '这里是详细的文章内容...',
    category: 'troubleshoot',
    createTime: '2024-01-15',
    views: 1250,
    likes: 89,
    author: '李工程师'
  },
  {
    id: '2',
    title: '打印机日常维护指南',
    description: '打印机日常维护的详细步骤，包括清洁、耗材更换、故障预防等。',
    content: '这里是详细的文章内容...',
    category: 'guide',
    createTime: '2024-01-14',
    views: 856,
    likes: 67,
    author: '张工程师'
  },
  {
    id: '3',
    title: '常见网络连接问题FAQ',
    description: '收集了用户最常遇到的网络连接问题及解决方案。',
    content: '这里是详细的文章内容...',
    category: 'faq',
    createTime: '2024-01-13',
    views: 2100,
    likes: 156,
    author: '王工程师'
  },
  {
    id: '4',
    title: '软件安装失败排查方法',
    description: '系统介绍软件安装失败的各种原因和对应的解决方法。',
    content: '这里是详细的文章内容...',
    category: 'troubleshoot',
    createTime: '2024-01-12',
    views: 980,
    likes: 73,
    author: '刘工程师'
  }
]

// 获取分类类型
const getCategoryType = (category: string): import('vant/lib/tag/types').TagType => {
  const categoryMap: Record<string, import('vant/lib/tag/types').TagType> = {
    faq: 'primary',
    guide: 'success',
    troubleshoot: 'warning'
  }
  return categoryMap[category] || 'default'
}

// 获取分类文本
const getCategoryText = (category: string) => {
  const categoryMap: Record<string, string> = {
    faq: '常见问题',
    guide: '操作指南',
    troubleshoot: '故障排除'
  }
  return categoryMap[category] || '其他'
}

// 加载数据
const loadData = () => {
  // 模拟API调用
  setTimeout(() => {
    let filteredArticles = mockArticles
    
    // 按分类筛选
    if (props.category !== 'all') {
      filteredArticles = mockArticles.filter(article => article.category === props.category)
    }
    
    // 按搜索关键词筛选
    if (props.search) {
      filteredArticles = filteredArticles.filter(article => 
        article.title.toLowerCase().includes(props.search.toLowerCase()) ||
        article.description.toLowerCase().includes(props.search.toLowerCase())
      )
    }
    
    articles.value = filteredArticles
    loading.value = false
    finished.value = true
  }, 1000)
}

// 下拉刷新
const onRefresh = () => {
  finished.value = false
  loadData()
  refreshing.value = false
}

// 上拉加载
const onLoad = () => {
  loadData()
}

// 查看文章详情
const viewArticle = (article: Article) => {
  // 这里可以跳转到文章详情页面
  showToast('查看文章详情')
}

// 点赞文章
const likeArticle = (id: string) => {
  showToast('点赞成功')
  // 这里应该调用API更新点赞数
}

// 分享文章
const shareArticle = (article: Article) => {
  showToast('分享功能开发中...')
}

// 滚动事件处理函数
const handleScroll = () => {
  if (!scrollContainer.value) return
  
  // 显示滚动条
  scrollContainer.value.classList.add('show-scrollbar')
  
  // 清除之前的定时器
  if (scrollTimer.value) {
    clearTimeout(scrollTimer.value)
  }
  
  // 设置新的定时器，1秒后隐藏滚动条
  scrollTimer.value = window.setTimeout(() => {
    if (scrollContainer.value) {
      scrollContainer.value.classList.remove('show-scrollbar')
    }
    scrollTimer.value = null
  }, 1000)
}

// 监听搜索和分类变化
watch([() => props.category, () => props.search], () => {
  loadData()
})

onMounted(() => {
  loadData()
  // 添加滚动事件监听
  if (scrollContainer.value) {
    scrollContainer.value.addEventListener('scroll', handleScroll)
  }
})

// 移除滚动事件监听
onBeforeUnmount(() => {
  if (scrollContainer.value) {
    scrollContainer.value.removeEventListener('scroll', handleScroll)
  }
  if (scrollTimer.value) {
    clearTimeout(scrollTimer.value)
  }
})
</script>

<style lang="scss" scoped>
.knowledge-list {
  height: calc(100vh - 46px - 50px);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  -webkit-overflow-scrolling: touch;
  
  &.show-scrollbar {
    scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
  }
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: transparent;
    border-radius: 3px;
  }
  
  &.show-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
  }

  .article-list {
    .article-item {
      margin-bottom: 12px;
      cursor: pointer;
      transition: transform 0.2s;

      &:active {
        transform: scale(0.98);
      }

      .article-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;

        .article-title {
          flex: 1;
          font-size: 16px;
          font-weight: 600;
          color: var(--text-color);
          line-height: 1.4;
        }
      }

      .article-content {
        .article-desc {
          font-size: 14px;
          color: var(--text-color-2);
          line-height: 1.5;
          margin-bottom: 12px;
        }

        .article-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;

          .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: var(--text-color-3);

            .van-icon {
              font-size: 14px;
            }
          }
        }
      }

      .article-footer {
        margin-top: 12px;
        display: flex;
        gap: 8px;
      }
    }
  }
}
</style>