// 暂时注释掉不存在的导入，等待后续实现
// import field from '@/api/servicetrack/field';
// import { Page, PageRowField, FieldItem } from '@/api/servicetrack/field/types';
// import { FieldTypeEnum, FieldIdEnum } from '@/enums/STCommonEnum';

export const transformPageData = (rawData: any[]): any[] => {
  // Group data by unique page
  const pageGroups: Record<string, any[]> = rawData.reduce((acc: Record<string, any[]>, item: any) => {
    const pageKey = `${item.pageId}-${item.pageName}`;
    if (!acc[pageKey]) {
      acc[pageKey] = [];
    }
    acc[pageKey].push(item);
    return acc;
   }, {});
  
    // Transform each page group
   return Object.entries(pageGroups).map(([pageKey, pageItems]) => {
    const [pageId, pageName] = pageKey.split('-');
    // Group fields by row
    const rowFieldGroups: Record<string, any[]> = pageItems.reduce((acc: Record<string, any[]>, item: any) => {
        if (!acc[item.pageRow]) {
          acc[item.pageRow] = [];
        }
       // ifMandatory: 0, // Default value, as not specified in original data
       // ifVisible: 1, // Default value, as not specified in original data
        item.attributes = {
          supportHTMLFormat: false,
          ifVisible: true,
          ifMandatory: false,
          ifDisabled: false
        }
        if (item.fieldType === "MULTILINE_EDIT_BOX") { // 暂时使用字符串代替枚举
         item.attributes.supportHTMLFormat = true;
        }
        if (typeof item.ifDisabled !== 'undefined') {
          item.attributes.ifDisabled = item.ifDisabled;
        }
        if (typeof item.ifMandatory !== 'undefined') {
          item.attributes.ifMandatory = item.ifMandatory;
        }
        if (typeof item.ifVisible !== 'undefined') {
          item.attributes.ifVisible = item.ifVisible;
        }
        if( typeof item.formula !== 'undefined' && item.formula) {
          item.attributes.formula = item.formula;
        }
       
        acc[item.pageRow].push({
        fieldId: item.fieldId,
        defaultName: item.fieldName, // Using fieldName as defaultName
        fieldName: item.fieldName,
        fieldTypeId: item.fieldType,
        fieldSubtype: item.fieldSubtype || 0,
        ifMandatory: item.ifMandatory || false,
        ifVisible: item.ifVisible || true,
        ColumnId: item.pageColumn,
        attributes: item.attributes || {},
        parentFieldId: item.parentFieldId || 0,
        childFieldIds: item.childFieldIds || []
       });
      return acc;
    }, {});
  
    // Transform row groups into PageRowField structure
    const fields: any[] = Object.entries(rowFieldGroups).map(([row, columnFields]) => ({ // 暂时使用any[]代替具体类型
      row: parseInt(row),
      ColumnFields: (columnFields as any[]).sort((a, b) => a.ColumnId - b.ColumnId) // 暂时使用any代替具体类型
    }));
    return {
      pageId: parseInt(pageId),
      pageName: pageName,
      fields: fields
    };
  });
};