<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人资料 - ITSM系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background-color: #f5f7fa;
            width: 100%;
            max-width: 430px;
            margin: 0 auto;
            height: 100vh;
            overflow: hidden;
            position: relative;
            border-radius: 40px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        
        .app-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            background-color: white;
            border-radius: 40px;
        }
        
        .status-bar {
            height: 44px;
            width: 100%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            position: relative;
            z-index: 10;
            border-top-left-radius: 40px;
            border-top-right-radius: 40px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .tab-bar {
            height: 84px;
            width: 100%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-around;
            border-top: 1px solid rgba(0,0,0,0.05);
            padding-bottom: 20px;
            border-bottom-left-radius: 40px;
            border-bottom-right-radius: 40px;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 50px;
            color: #999;
        }
        
        .tab-item.active {
            color: #409EFF;
        }
        
        .content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 15px;
            background-color: #f5f7fa;
        }
        
        .card {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .btn-primary {
            background-color: #409EFF;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 500;
            font-size: 16px;
            text-align: center;
            width: 100%;
            display: block;
        }
        
        .btn-outline {
            background-color: transparent;
            color: #409EFF;
            border: 1px solid #409EFF;
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 500;
            font-size: 16px;
            text-align: center;
            width: 100%;
            display: block;
        }
        
        .profile-header {
            background-color: #409EFF;
            color: white;
            padding: 30px 20px;
            border-radius: 12px 12px 0 0;
            position: relative;
            overflow: hidden;
        }
        
        .profile-header:before {
            content: '';
            position: absolute;
            top: -20px;
            right: -20px;
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .profile-header:after {
            content: '';
            position: absolute;
            bottom: -30px;
            left: -30px;
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .menu-item:last-child {
            border-bottom: none;
        }
        
        .menu-icon {
            width: 22px;
            height: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: #409EFF;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="status-bar">
            <h1 class="text-lg font-bold">个人资料</h1>
        </div>
        
        <div class="content pb-4">
            <!-- 个人资料头部 -->
            <div class="card mb-4 overflow-hidden">
                <div class="profile-header">
                    <div class="flex items-center">
                        <div class="w-16 h-16 rounded-full bg-white flex items-center justify-center overflow-hidden mr-4">
                            <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&h=150&q=80" alt="用户头像" class="w-full h-full object-cover">
                        </div>
                        <div>
                            <h2 class="text-xl font-bold">张三</h2>
                            <p class="text-sm opacity-80">产品部 · 产品经理</p>
                        </div>
                    </div>
                    <div class="flex mt-4">
                        <div class="flex-1">
                            <div class="text-3xl font-bold">15</div>
                            <div class="text-xs opacity-80">已提交工单</div>
                        </div>
                        <div class="flex-1">
                            <div class="text-3xl font-bold">12</div>
                            <div class="text-xs opacity-80">已完成工单</div>
                        </div>
                        <div class="flex-1">
                            <div class="text-3xl font-bold">3</div>
                            <div class="text-xs opacity-80">处理中工单</div>
                        </div>
                    </div>
                </div>
                
                <div class="p-4 flex justify-between">
                    <button class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-600 mr-2"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                        <span class="text-sm text-gray-600">个人信息</span>
                    </button>
                    <button class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-600 mr-2"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg>
                        <span class="text-sm text-gray-600">编辑资料</span>
                    </button>
                </div>
            </div>
            
            <!-- 信息卡片 -->
            <div class="card mb-4">
                <div class="menu-item">
                    <div class="menu-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line></svg>
                    </div>
                    <div class="flex-1">
                        <span class="text-gray-800">关于</span>
                    </div>
                    <div>
                        <span class="text-xs text-gray-400">v1.0.0</span>
                    </div>
                </div>
                
                <div class="menu-item">
                    <div class="menu-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg>
                    </div>
                    <div class="flex-1">
                        <span class="text-gray-800">隐私政策</span>
                    </div>
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400"><polyline points="9 18 15 12 9 6"></polyline></svg>
                    </div>
                </div>
            </div>
            
            <!-- 退出登录按钮 -->
            <button class="btn-outline mt-4">退出登录</button>
        </div>
        
        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <a href="home.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                    <polyline points="9 22 9 12 15 12 15 22"></polyline>
                </svg>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="modules.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="3" y="3" width="7" height="7"></rect>
                    <rect x="14" y="3" width="7" height="7"></rect>
                    <rect x="14" y="14" width="7" height="7"></rect>
                    <rect x="3" y="14" width="7" height="7"></rect>
                </svg>
                <span class="text-xs mt-1">模块</span>
            </a>
            <a href="reports.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="12" y1="20" x2="12" y2="10"></line>
                    <line x1="18" y1="20" x2="18" y2="4"></line>
                    <line x1="6" y1="20" x2="6" y2="16"></line>
                </svg>
                <span class="text-xs mt-1">报表</span>
            </a>
            <a href="profile.html" class="tab-item active">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                </svg>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
</body>
</html>
