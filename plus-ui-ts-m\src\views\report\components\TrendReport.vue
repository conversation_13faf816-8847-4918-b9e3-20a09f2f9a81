<template>
  <div class="page-container">
    <van-nav-bar title="趋势图" fixed left-arrow @click-left="$router.back()"/>
    <div class="report-content">
      <div ref="chartContainer" class="chart-container"></div>
    </div>
    <!-- 底部导航栏 -->
    <van-tabbar route fixed>
      <van-tabbar-item to="/home" icon="home-o">首页</van-tabbar-item>
      <van-tabbar-item to="/projects" icon="apps-o">项目</van-tabbar-item>
      <van-tabbar-item to="/reports" icon="chart-trending-o">报表</van-tabbar-item>
      <van-tabbar-item to="/profile" icon="user-o">我的</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'

const chartContainer = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

// 示例数据
const months = ['1月', '2月', '3月', '4月', '5月', '6月']
const incidentData = [120, 132, 101, 134, 90, 230]
const requestData = [220, 182, 191, 234, 290, 330]

const initChart = () => {
  if (chartContainer.value) {
    // 销毁之前的实例
    if (chartInstance) {
      chartInstance.dispose()
    }
    
    // 初始化新的实例
    chartInstance = echarts.init(chartContainer.value)
    
    // 配置图表选项
    const option = {
      title: {
        text: '服务请求与故障趋势'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['服务请求', '故障报告']
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: months
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '服务请求',
          type: 'line',
          data: requestData,
          smooth: true
        },
        {
          name: '故障报告',
          type: 'line',
          data: incidentData,
          smooth: true
        }
      ]
    }
    
    // 设置配置项
    chartInstance.setOption(option)
  }
}

const toggleTimeRange = () => {
  console.log('切换时间范围')
  // 这里可以实现时间范围切换功能
}

onMounted(() => {
  console.log('趋势图报表页面已加载')
  initChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (chartInstance) {
      chartInstance.resize()
    }
  })
})

onBeforeUnmount(() => {
  // 销毁图表实例
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})
</script>

<style scoped>
.page-container {
    position: relative;
    min-height: 100vh;
    padding-top: 46px;
    padding-bottom: 50px;
    box-sizing: border-box;
  }

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px 0;
  border-bottom: 1px solid #e4e7ed;
}

.report-content {
  background-color: white;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.chart-container {
  width: 100%;
  height: 300px;
}
</style>