<template>
  <div class="timeline-container">
    <div class="timeline">
      <div class="timeline-item" v-for="(item, index) in progressData" :key="index">
        <div class="timeline-title">{{ item.stateToName }}</div>
        <div class="timeline-time">{{ formatDateTime(item.dateTime) }}</div>
        <div class="timeline-desc">负责人: {{ item.userName }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { getItemHistory } from '@/api/item';
import type { HistoryInfoVO } from '@/api/item/types';

// 定义组件的props
const props = defineProps({
  projectId: {
    type: Number,
    required: true
  },
  itemId: {
    type: Number,
    required: true
  }
});

// 进度数据
const progressData = ref<HistoryInfoVO[]>([]);

// 格式化日期时间
const formatDateTime = (dateTime: string) => {
  // 简单的日期格式化，实际项目中可能需要更复杂的处理
  return dateTime ? dateTime.replace('T', ' ').substring(0, 16) : '';
};

// 获取处理进度数据
const fetchProgressData = async () => {
  try {
    const response = await getItemHistory(props.projectId, props.itemId);
    // 从response.data中获取实际数据
    progressData.value = response.data.historyList || [];
  } catch (error) {
    console.error('获取处理进度数据失败:', error);
    // 如果API调用失败，使用模拟数据
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchProgressData();
});
</script>

<style scoped>
.timeline {
  position: relative;
  padding-left: 25px;
}

.timeline:before {
  content: '';
  position: absolute;
  left: 5px;
  top: 10px;
  bottom: 0;
  width: 2px;
  background-color: #e0e0e0;
}

.timeline-item {
  position: relative;
  margin-bottom: 20px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-item:before {
  content: '';
  position: absolute;
  left: -25px;
  top: 4px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #409EFF;
  border: 2px solid #fff;
  z-index: 1;
}

.timeline-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.timeline-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.timeline-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}
</style>