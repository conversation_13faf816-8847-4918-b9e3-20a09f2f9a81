import request from '@/utils/request';
import type { AxiosResponse } from 'axios';
import { parseStrEmpty } from '@/utils/ruoyi';
import type { ItemQuery, ListItemVo, stateVO, HistoryInfoVO, ProjectMemberVo, ItemVO} from './types';

// pc端固定客户端授权id
const clientId = import.meta.env.VITE_APP_CLIENT_ID;

/**
 * @param data {LoginData}
 * @returns
 */
export const listItem = (query: ItemQuery): Promise<AxiosResponse<ListItemVo[]>> => {
  return request({
    url: '/servicetrack/item/list',
    method: 'get',
    params: query
  });
};

export const getStateList = (projectId: number): Promise<AxiosResponse<stateVO[]>> => {
  return request({
    url: '/servicetrack/project/getStateList' + '?projectId=' + parseStrEmpty(projectId),
    method: 'get'
  });
};

export const getMemberList = (projectId: number): Promise<AxiosResponse<ProjectMemberVo[]>> => {
  return request({
    url: '/servicetrack/project/getMemberList' + '?projectId=' + parseStrEmpty(projectId),
    method: 'get'
  });
};

export const getItemHistory = (projectId: number, itemId: number): Promise<AxiosResponse<HistoryInfoVO[]>> => {
  return request({
    url: '/servicetrack/item/getHistory' + '?projectId=' + parseStrEmpty(projectId) + '&itemId=' + parseStrEmpty(itemId),
    method: 'get'
  });
};

export const getItem = (projectId?: string | number, itemId?: string | number): Promise<AxiosResponse<ItemVO>> => {
  return request({
    url: '/servicetrack/item/getInfo' + '?projectId=' + parseStrEmpty(projectId) + '&itemId=' + parseStrEmpty(itemId),
    method: 'get'
  });
};