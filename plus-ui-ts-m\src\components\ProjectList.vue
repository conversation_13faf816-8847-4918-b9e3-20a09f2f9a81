<template>
  <div v-if="projects.length === 0" class="empty-state">
    <p>暂无项目数据</p>
  </div>
  <div v-else class="project-list">
    <div 
      v-for="(project, index) in projects" 
      :key="project.id"
      class="project-item" 
      @click="handleprojectClick(project)"
    >
      <div class="project-icon" :style="{ backgroundColor: getIconBackgroundColor(index) }">
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          width="24" 
          height="24" 
          viewBox="0 0 24 24" 
          fill="none" 
          :stroke="getIconColor(index)" 
          stroke-width="2" 
          stroke-linecap="round" 
          stroke-linejoin="round"
        >
          <path v-if="index % 5 === 0" d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
          <template v-else-if="index % 5 === 1">
            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
            <line x1="12" y1="9" x2="12" y2="13"></line>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
          </template>
          <template v-else-if="index % 5 === 2">
            <polyline points="4 17 10 11 4 5"></polyline>
            <line x1="12" y1="19" x2="20" y2="19"></line>
          </template>
          <template v-else-if="index % 5 === 3">
            <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path>
            <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>
          </template>
          <template v-else>
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </template>
        </svg>
      </div>
      <div class="project-info">
        <div class="project-title">{{ project.projectName }}</div>
        <div class="project-desc">{{ project.projectDescription || '暂无描述' }}</div>
      </div>
      <div v-if="project.openIncidentCount > 0" class="badge-count">
        {{ project.openIncidentCount }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import type { ProjectListData } from '@/api/general/types'

interface Props {
  projects: ProjectListData[]
}

const props = defineProps<Props>()
const router = useRouter()

const handleprojectClick = (project: ProjectListData) => {
  // 跳转到工单列表页面，并传递项目ID
  router.push(`/list?projectId=${project.projectId}`)
}

const getIconBackgroundColor = (index: number) => {
  const colors = [
    'rgba(64, 158, 255, 0.1)',   // 蓝色
    'rgba(230, 162, 60, 0.1)',   // 橙色
    'rgba(103, 194, 58, 0.1)',   // 绿色
    'rgba(151, 117, 250, 0.1)',  // 紫色
    'rgba(156, 39, 176, 0.1)'    // 深紫色
  ]
  return colors[index % colors.length]
}

const getIconColor = (index: number) => {
  const colors = [
    '#409EFF',   // 蓝色
    '#E6A23C',   // 橙色
    '#67C23A',   // 绿色
    '#9775FA',   // 紫色
    '#9C27B0'    // 深紫色
  ]
  return colors[index % colors.length]
}
</script>

<style scoped>
.project-list {
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.project-item {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  transition: background-color 0.2s;
}

.project-item:last-child {
  border-bottom: none;
}

.project-item:active {
  background-color: #f0f0f0;
}

.project-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  border-radius: 8px;
}

.project-info {
  flex: 1;
}

.project-title {
  font-weight: 500;
  font-size: 16px;
  color: #333;
}

.project-desc {
  font-size: 13px;
  color: #999;
  margin-top: 4px;
}

.badge-count {
  background-color: #F56C6C;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
  font-size: 14px;
}
</style> 