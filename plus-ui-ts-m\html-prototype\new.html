<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建工单 - ITSM系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background-color: #f5f7fa;
            width: 100%;
            max-width: 430px;
            margin: 0 auto;
            height: 100vh;
            overflow: hidden;
            position: relative;
            border-radius: 40px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        
        .app-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            background-color: white;
            border-radius: 40px;
        }
        
        .status-bar {
            height: 44px;
            width: 100%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            position: relative;
            z-index: 10;
            border-top-left-radius: 40px;
            border-top-right-radius: 40px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 15px;
            background-color: #f5f7fa;
        }
        
        .card {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .btn-primary {
            background-color: #409EFF;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 500;
            font-size: 16px;
            text-align: center;
            width: 100%;
            display: block;
        }
        
        .btn-outline {
            background-color: transparent;
            color: #409EFF;
            border: 1px solid #409EFF;
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 500;
            font-size: 16px;
            text-align: center;
            width: 100%;
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-size: 14px;
            color: #333;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            padding: 12px 15px;
            font-size: 14px;
            transition: all 0.3s;
            background: #fff;
        }
        
        .form-control:focus {
            border-color: #409EFF;
            outline: none;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
        
        textarea.form-control {
            min-height: 120px;
            resize: vertical;
        }
        
        .select-wrapper {
            position: relative;
        }
        
        .select-wrapper:after {
            content: '';
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #666;
            pointer-events: none;
        }
        
        select.form-control {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            padding-right: 30px;
        }
        
        .file-upload {
            position: relative;
            overflow: hidden;
            display: inline-block;
            width: 100%;
        }
        
        .file-upload input[type=file] {
            position: absolute;
            left: 0;
            top: 0;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .file-upload-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            padding: 12px 15px;
            background-color: #f0f2f5;
            border: 1px dashed #d9d9d9;
            border-radius: 8px;
            color: #666;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .file-upload-btn:hover {
            border-color: #409EFF;
            color: #409EFF;
        }
        
        .file-preview {
            display: flex;
            align-items: center;
            background: #f9fafc;
            padding: 10px;
            border-radius: 8px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="status-bar">
            <h1 class="text-lg font-bold">创建工单</h1>
        </div>
        
        <div class="content">
            <form id="workOrderForm" class="p-1">
                <!-- 客户信息 -->
                <div class="form-group">
                    <label for="customer" class="form-label">客户 <span class="text-red-500">*</span></label>
                    <div class="select-wrapper">
                        <select id="customer" class="form-control" required>
                            <option value="">请选择客户</option>
                            <option value="customer1">客户A</option>
                            <option value="customer2">客户B</option>
                            <option value="customer3">客户C</option>
                            <option value="customer4">客户D</option>
                            <option value="customer5">客户E</option>
                        </select>
                    </div>
                </div>
                
                <!-- 工单类型 -->
                <div class="form-group">
                    <label for="type" class="form-label">工单类型 <span class="text-red-500">*</span></label>
                    <div class="select-wrapper">
                        <select id="type" class="form-control" required>
                            <option value="">请选择工单类型</option>
                            <option value="network">网络问题</option>
                            <option value="software">软件安装</option>
                            <option value="hardware">硬件故障</option>
                            <option value="account">账户权限</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                </div>
                
                <!-- 工单标题 -->
                <div class="form-group">
                    <label for="title" class="form-label">工单标题 <span class="text-red-500">*</span></label>
                    <input type="text" id="title" class="form-control" placeholder="请输入工单标题，简要描述问题" required>
                </div>
                
                <!-- 工单详情 -->
                <div class="form-group">
                    <label for="description" class="form-label">问题描述 <span class="text-red-500">*</span></label>
                    <textarea id="description" class="form-control" placeholder="请详细描述您遇到的问题，包括症状、影响、发生时间等信息" required></textarea>
                </div>
                
                <!-- 优先级 -->
                <div class="form-group">
                    <label for="priority" class="form-label">优先级 <span class="text-red-500">*</span></label>
                    <div class="select-wrapper">
                        <select id="priority" class="form-control" required>
                            <option value="">请选择优先级</option>
                            <option value="low">低 - 不急需解决</option>
                            <option value="medium">中 - 影响工作但有替代方案</option>
                            <option value="high">高 - 严重影响工作效率</option>
                            <option value="urgent">紧急 - 无法工作</option>
                        </select>
                    </div>
                </div>
                
                <!-- 上传附件 -->
                <div class="form-group">
                    <label class="form-label">上传附件</label>
                    <div class="file-upload">
                        <div class="file-upload-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="17 8 12 3 7 8"></polyline><line x1="12" y1="3" x2="12" y2="15"></line></svg>
                            点击选择文件或拖放文件至此
                        </div>
                        <input type="file" id="attachment" multiple aria-label="上传附件" title="选择文件上传">
                    </div>
                    
                    <div id="filePreview" class="mt-3">
                        <!-- 文件预览区域 - 将由JS动态填充 -->
                    </div>
                </div>
                
                <!-- 联系方式 -->
                <div class="form-group">
                    <label for="contact" class="form-label">联系方式</label>
                    <input type="text" id="contact" class="form-control" placeholder="请输入您的联系方式，如电话号码">
                </div>
                
                <!-- 提交按钮 -->
                <div class="mt-6 mb-4">
                    <button type="submit" class="btn-primary">提交工单</button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        // 文件上传预览
        document.getElementById('attachment').addEventListener('change', function(e) {
            const filePreview = document.getElementById('filePreview');
            filePreview.innerHTML = '';
            
            if (this.files.length > 0) {
                for (let i = 0; i < this.files.length; i++) {
                    const file = this.files[i];
                    const fileSize = (file.size / 1024).toFixed(1) + 'KB';
                    
                    const previewDiv = document.createElement('div');
                    previewDiv.className = 'file-preview mb-2';
                    
                    let fileIcon = '';
                    if (file.type.startsWith('image/')) {
                        fileIcon = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#409EFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect><circle cx="8.5" cy="8.5" r="1.5"></circle><polyline points="21 15 16 10 5 21"></polyline></svg>';
                    } else {
                        fileIcon = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#409EFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg>';
                    }
                    
                    previewDiv.innerHTML = `
                        ${fileIcon}
                        <div class="flex-1">
                            <div class="text-sm font-medium">${file.name}</div>
                            <div class="text-xs text-gray-500">${fileSize}</div>
                        </div>
                        <button type="button" class="remove-file p-1" data-index="${i}">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#F56C6C" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                        </button>
                    `;
                    
                    filePreview.appendChild(previewDiv);
                }
                
                // 添加删除文件的事件处理
                document.querySelectorAll('.remove-file').forEach(button => {
                    button.addEventListener('click', function() {
                        const index = this.getAttribute('data-index');
                        this.closest('.file-preview').remove();
                    });
                });
            }
        });
        
        // 表单提交处理
        document.getElementById('workOrderForm').addEventListener('submit', function(e) {
            e.preventDefault();
            // 在实际应用中，这里会发送AJAX请求到后端创建工单
            
            // 模拟成功后跳转回工单列表页
            alert('工单创建成功！');
            window.location.href = 'work-order.html';
        });
    </script>
</body>
</html>
