import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from 'unplugin-vue-components/resolvers'
import autoprefixer from 'autoprefixer'
import postcssPxToViewport from 'postcss-px-to-viewport-8-plugin'
import VueDevTools from 'vite-plugin-vue-devtools'

export default defineConfig(({ command, mode }) => {
  const isProduction = mode === 'production'
  
  // 加载环境变量
  const env = loadEnv(mode, process.cwd())
  return {
    base: env.VITE_BASE_URL || '/', // 使用环境变量
    plugins: [
      vue(),
      ...(command === 'serve' ? [VueDevTools()] : []),
      AutoImport({
        imports: ['vue', 'vue-router', 'pinia'],
        dts: 'src/auto-imports.d.ts',
      }),
      Components({
        resolvers: [VantResolver({ importStyle: false })],
        dts: 'src/components.d.ts',
      }),
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
      },
    },
    css: {
      postcss: {
        plugins: [
          postcssPxToViewport({
            unitToConvert: 'px',
            viewportWidth: 375,
            unitPrecision: 5,
            propList: ['*'],
            viewportUnit: 'vw',
            fontViewportUnit: 'vw',
            selectorBlackList: [],
            minPixelValue: 1,
            mediaQuery: false,
            replace: true,
            exclude: [/node_modules/],
            landscape: false,
            landscapeUnit: 'vw',
            landscapeWidth: 568,
          }),
          autoprefixer(),
        ],
      },
    },
    server: {
      host: '0.0.0.0',
      port: Number(env.VITE_APP_PORT),
      open: true,
      proxy: {
        [env.VITE_APP_BASE_API]: {
          target: env.VITE_API_BASE_URL || 'http://localhost:8080',
          changeOrigin: true,
          ws: true,
          rewrite: (path) => path.replace(new RegExp('^' + env.VITE_APP_BASE_API), '')
        }
      }
    },
    // 预编译
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'axios',
        '@vueuse/core',
        'echarts',
        'vue-i18n',
        '@vueup/vue-quill'      
      ]
    },
    esbuild: {
      target: ['esnext']
    },
    build: {
      target: ['esnext'],
      commonjsOptions: {
        include: [/node_modules/]
      },
      outDir: process.env.VITE_BUILD_OUTPUT_DIR || 'dist',
      assetsDir: 'assets',
      sourcemap: process.env.VITE_GENERATE_SOURCEMAP === 'true',
      minify: process.env.VITE_ENABLE_MINIFICATION !== 'false',
      rollupOptions: {
        output: {
          // 代码分割配置
          manualChunks: process.env.VITE_ENABLE_JS_CODE_SPLITTING === 'true' ? {
            vendor: ['vue', 'vue-router', 'pinia'],
            vant: ['vant'],
            utils: ['axios'],
          } : undefined,
          // 资源文件命名
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
        },
      },
      // 启用chunk大小警告
      chunkSizeWarningLimit: parseInt(process.env.VITE_MAX_CHUNK_SIZE || '500'),
    },
    // 生产环境优化
    ...(isProduction && {
      esbuild: {
        drop: ['debugger'],
      },
    }),
  }
}) 