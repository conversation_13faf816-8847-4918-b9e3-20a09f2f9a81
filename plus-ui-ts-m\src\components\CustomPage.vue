<template>
  <div class="custom-page">
    <van-collapse 
      v-for="(pageInfo, index) in pageInfos" 
      :key="pageInfo.pageId"
      v-model="fieldListActiveNames[index]" 
      class="detail-card"
    >
      <van-collapse-item 
        :title="pageInfo.pageName" 
        name="fieldList"
      >
        <van-cell-group :border="false">
          <van-cell 
            v-for="field in pageInfo.fields" 
            :key="field.fieldId"
            :title="field.fieldName"
            class="vertical-cell"
          >
            <template #default>
              <div class="field-value-vertical">
                <!-- 用户类型字段显示头像 -->
                <template v-if="isUserField(field)">
                  <div class="user-info">
                    <img class="avatar-small" :src="getFieldValue(field.fieldId)?.avatar" />
                    <span>{{ getFieldValue(field.fieldId)?.name }}</span>
                  </div>
                </template>
                
                <!-- 单行文本输入框 (SHORT_TEXT) - 可直接编辑 -->
                <template v-else-if="field.fieldType === FieldTypeEnum.SHORT_TEXT">
                  <div 
                    v-show="editableFieldId !== field.fieldId"
                    class="normal-value editable-field-container field-value-left consistent-height" 
                    @click="editField(field, $event as any)"
                    :data-field-id="field.fieldId"
                  >
                    <span v-if="getFieldValue(field.fieldId)" class="normal-value">{{ getFieldValue(field.fieldId) }}</span>
                    <span v-else class="empty-value">空</span>
                  </div>
                  <van-field
                    v-show="editableFieldId === field.fieldId"
                    :model-value="getFieldValue(field.fieldId)"
                    @update:model-value="val => updateFieldValue(field.fieldId, val)"
                    @blur="saveField(field.fieldId)"
                    @keyup.enter="saveField(field.fieldId)"
                    :data-field-id="field.fieldId"
                  />
                </template>
                
                <!-- 多行文本输入框 (PLAIN_TEXT) - 点击弹出popup -->
                <template v-else-if="field.fieldType === FieldTypeEnum.PLAIN_TEXT">
                  <div 
                    class="normal-value editable-field-container field-value-left consistent-height"
                    @click="editField(field, $event as any)"
                  >
                    <span v-if="getFieldValue(field.fieldId)" class="normal-value">{{ getFieldDisplayValue(field) }}</span>
                    <span v-else class="empty-value">空</span>
                  </div>
                </template>

                <!-- 单选下拉框 (DROPDOWN_LIST) - 点击弹出popup -->
                <template v-else-if="field.fieldType === FieldTypeEnum.DROPDOWN_LIST">
                  <div 
                    class="normal-value editable-field-container field-value-left consistent-height"
                    @click="editField(field, $event as any)"
                  >
                    <span v-if="getFieldValue(field.fieldId)" class="normal-value">{{ getFieldDisplayValue(field) }}</span>
                    <span v-else class="empty-value">空</span>
                  </div>
                </template>
                
                <!-- 多选框 (MULTIPLE_SELECTION_LISTBOX) - 点击弹出popup -->
                <template v-else-if="field.fieldType === FieldTypeEnum.MULTIPLE_SELECTION_LISTBOX">
                  <div 
                    class="normal-value editable-field-container field-value-left consistent-height"
                    @click="editField(field, $event as any)"
                  >
                    <span v-if="getFieldValue(field.fieldId)" class="normal-value">{{ getFieldDisplayValue(field) }}</span>
                    <span v-else class="empty-value">空</span>
                  </div>
                </template>

                <!-- 多行输入框,富文本输入框 (MULTILINE_EDIT_BOX) - 点击弹出popup -->
                <template v-else-if="field.fieldType === FieldTypeEnum.MULTILINE_EDIT_BOX">
                  <div 
                    class="normal-value editable-field-container field-value-left consistent-height"
                    @click="editField(field, $event as any)"
                  >
                    <span v-if="getFieldValue(field.fieldId)" class="normal-value">{{ getFieldDisplayValue(field) }}</span>
                    <span v-else class="empty-value">空</span>
                  </div>
                </template>

                <!-- 日期时间字段 (DATE_TIME_FIELD) - 点击弹出popup -->
                <template v-else-if="field.fieldType === FieldTypeEnum.DATE_TIME_FIELD">
                  <div 
                    class="normal-value editable-field-container field-value-left consistent-height"
                    @click="editField(field, $event as any)"
                  >
                    <span v-if="getFieldValue(field.fieldId)" class="normal-value">{{ getFieldValue(field.fieldId) }}</span>
                    <span v-else class="empty-value">空</span>
                  </div>
                </template>
                
                <!-- 复选框字段 (CHECKBOX) - 点击弹出popup -->
                <template v-else-if="field.fieldType === FieldTypeEnum.CHECKBOX">
                  <div 
                    class="normal-value editable-field-container field-value-left consistent-height"
                    @click="editField(field, $event as any)"
                  >
                    <span v-if="getFieldValue(field.fieldId)" class="normal-value">{{ getFieldDisplayValue(field) }}</span>
                    <span v-else class="empty-value">空</span>
                  </div>
                </template>

                <!-- 单选按钮字段 (RADIO_BUTTON) - 点击弹出popup -->
                <template v-else-if="field.fieldType === FieldTypeEnum.RADIO_BUTTON">
                  <div 
                    class="normal-value editable-field-container field-value-left consistent-height"
                    @click="editField(field, $event as any)"
                  >
                    <span v-if="getFieldValue(field.fieldId)" class="normal-value">{{ getFieldDisplayValue(field) }}</span>
                    <span v-else class="empty-value">空</span>
                  </div>
                </template>

                <!-- 组合框字段 (COMBOBOX) - 点击弹出popup -->
                <template v-else-if="field.fieldType === FieldTypeEnum.COMBOBOX">
                  <div 
                    class="normal-value editable-field-container field-value-left consistent-height"
                    @click="editField(field, $event as any)"
                  >
                    <span v-if="getFieldValue(field.fieldId)" class="normal-value">{{ getFieldDisplayValue(field) }}</span>
                    <span v-else class="empty-value">空</span>
                  </div>
                </template>

                <!-- 金额字段 -->
                <template v-else-if="field.fieldType === FieldTypeEnum.AMOUNT">
                  <div 
                    class="normal-value editable-field-container field-value-left consistent-height"
                    @click="editField(field, $event as any)"
                  >
                    <span v-if="getFieldValue(field.fieldId)" class="normal-value">{{ getFieldValue(field.fieldId) }}</span>
                    <span v-else class="empty-value">0.00</span>
                  </div>
                </template>

                <!-- 附件类型字段 -->
                <template v-else-if="isAttachmentField(field)">
                  <div class="attachment-item">
                    <van-icon name="description" class="attachment-icon" />
                    <div class="attachment-info">
                      <div class="attachment-name">{{ (getFieldValue(field.fieldId) as any)[0]?.name }}</div>
                      <div class="attachment-size">{{ (getFieldValue(field.fieldId) as any)[0]?.size }}</div>
                    </div>
                    <van-icon name="down" class="attachment-download" />
                  </div>
                </template>

                <!-- 用户类型字段 -->
                <template v-else-if="isUserField(field)">
                  <UserField :field="field" :field-value="getFieldValue(field.fieldId)" />
                </template>

                <!-- 位置类型字段 -->
                <template v-else-if="field.fieldType === 16">
                  <LocationField :field="field" :field-value="getFieldValue(field.fieldId)" />
                </template>

                <!-- 签名类型字段 -->
                <template v-else-if="field.fieldType === 15">
                  <SignatureField :field="field" :field-value="getFieldValue(field.fieldId)" />
                </template>

                <!-- 图片类型字段 -->
                <template v-else-if="field.fieldType === 18">
                  <ImageField :field="field" :field-value="getFieldValue(field.fieldId)" />
                </template>

                <!-- 文件类型字段 -->
                <template v-else-if="isAttachmentField(field)">
                  <FileField :field="field" :field-value="getFieldValue(field.fieldId)" />
                </template>

                <!-- 其他字段类型... -->
                <template v-else>
                  <span v-if="getFieldValue(field.fieldId)" class="normal-value">{{ getFieldValue(field.fieldId) }}</span>
                  <span v-else class="empty-value">无</span>
                </template>
              </div>
            </template>
            
          </van-cell>
        </van-cell-group>
      </van-collapse-item>
    </van-collapse>

    <!-- 通用字段编辑弹窗 -->
    <FieldEditPopup
      v-model:show="showFieldEditPopup"
      :field="currentEditField"
      :model-value="currentEditValue"
      :choices="currentEditChoices"
      @update:model-value="val => currentEditValue = val"
      @confirm="saveFieldValue"
      @cancel="showFieldEditPopup = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue'
import { FieldTypeEnum } from '@/enums/STCommonEnum'
import { getProjectPageSetting, listFieldChoices } from '@/api/field/index'
import { getItem } from '@/api/item/index'
import type { PageSettingVo, PageActionVo, ItemTypePageActionVo, Page, PageFieldVo, FieldChoiceBo, ChoiceItemMap } from '@/api/field/types'
import type { ItemVO, ItemFieldVO } from '@/api/item/types'

// 字段组件
import UserField from './fields/UserField.vue'
import LocationField from './fields/LocationField.vue'
import SignatureField from './fields/SignatureField.vue'
import ImageField from './fields/ImageField.vue'
import FileField from './fields/FileField.vue'

// 通用弹窗组件
import FieldEditPopup from './fields/FieldEditPopup.vue'

interface Props {
  projectId: number
  typeId: number
  itemId: number
}

const props = defineProps<Props>()

const fieldListActiveNames = ref<string[][]>([])
const pageInfos = ref<Array<{pageId: number, pageName: string, fields: PageFieldVo[]}>>([])
const itemData = ref<ItemVO | null>(null)
const fieldChoiceMap = ref<ChoiceItemMap>({})

// 可编辑字段相关数据
const editableFieldId = ref<number | null>(null)

// 通用弹窗相关数据
const showFieldEditPopup = ref(false)
const currentEditField = ref<PageFieldVo | null>(null)
const currentEditValue = ref<any>('')
const currentEditChoices = ref<Array<{ choiceId: number | string; choiceName: string }> | null>(null)

// 获取页面设置数据
const fetchPageSetting = async () => {
  try {
    const response = await getProjectPageSetting(props.projectId, 1)
    const pageSettingData: PageSettingVo = response.data
    
    // 根据typeId查找对应的PageActionVo
    const pageAction = pageSettingData.pageActions.find(action => action.typeId === props.typeId)
    
    if (pageAction) {
      // 查找所有actionId为-2且pageId大于500的ItemTypePageActionVo
      const itemTypePageActions = pageAction.itemTypePageActions.filter(item => item.actionId === -2 && item.pageId > 500)
      
      if (itemTypePageActions.length > 0) {
        // 获取所有符合条件的pageId和对应的pageName
        const pageInfoList: Array<{pageId: number, pageName: string, fields: PageFieldVo[]}> = []
        
        itemTypePageActions.forEach(item => {
          const page = pageSettingData.pages.find(p => p.pageId === item.pageId)
          if (page) {
            // 获取该页面下的所有字段，按照pageRow和pageColumn排序
            const pageFields = pageSettingData.pageFields
              .filter(field => field.pageId === item.pageId)
              .sort((a, b) => {
                // 首先按pageRow排序，然后按pageColumn排序
                if (a.pageRow !== b.pageRow) {
                  return a.pageRow - b.pageRow
                }
                return a.pageColumn - b.pageColumn
              })
            
            pageInfoList.push({
              pageId: item.pageId,
              pageName: page.pageName,
              fields: pageFields
            })
          }
        })
        
        // 设置页面信息
        pageInfos.value = pageInfoList
        
        // 为每个页面设置独立的折叠面板状态
        fieldListActiveNames.value = pageInfoList.map(() => ['fieldList'])
      }
    }
  } catch (error) {
    console.error('获取页面设置失败:', error)
  }
}

// 获取工单数据
const fetchItemData = async () => {
  try {
    const response = await getItem(props.projectId, props.itemId)
    itemData.value = response.data
    
    // 获取字段选项
    await fetchFieldChoices()
  } catch (error) {
    console.error('获取工单数据失败:', error)
  }
}

// 获取字段选项
const fetchFieldChoices = async () => {
  try {
    // 找出需要获取选项的字段ID（类型为3、4、8、10）
    const fieldIds: number[] = []
    
    // 遍历所有页面的字段
    for (const pageInfo of pageInfos.value) {
      for (const field of pageInfo.fields) {
        if ([3, 4, 8, 10].includes(field.fieldType) && !fieldIds.includes(field.fieldId)) {
          fieldIds.push(field.fieldId)
        }
      }
    }
    
    // 如果有需要获取选项的字段，则调用API
    if (fieldIds.length > 0) {
      const fieldChoiceBo: FieldChoiceBo = {
        projectId: props.projectId,
        fieldIds: fieldIds
      }
      
      const response = await listFieldChoices(fieldChoiceBo)
      console.log('Raw field choices response:', response.data);
      
      // 确保响应数据是对象格式，处理可能的字符串/数组问题
      const rawData = response.data;
      const processedData: ChoiceItemMap = {};
      
      for (const fieldId of fieldIds) {
        const choices = rawData[fieldId];
        console.log(`Choices for field ${fieldId}:`, choices, 'Type:', typeof choices);
        
        // 确保每个字段的choices是数组
        if (Array.isArray(choices)) {
          processedData[fieldId] = choices;
        } else if (typeof choices === 'string') {
          // 如果choices是字符串，可能是错误数据，创建空数组
          console.warn(`Field ${fieldId} choices is string: ${choices}, creating empty array`);
          processedData[fieldId] = [];
        } else if (choices && typeof choices === 'object') {
          // 如果是对象，尝试转换为数组
          const choicesArray = Object.values(choices).filter(item => 
            item && (typeof item === 'object' || typeof item === 'string')
          );
          processedData[fieldId] = choicesArray;
        } else {
          // 其他情况，创建空数组
          console.warn(`Invalid choices format for field ${fieldId}:`, choices);
          processedData[fieldId] = [];
        }
      }
      
      console.log('Processed field choices:', processedData);
      fieldChoiceMap.value = processedData;
    }
  } catch (error) {
    console.error('获取字段选项失败:', error)
    // 发生错误时，为所有字段创建空数组
    const fieldIds: number[] = [];
    for (const pageInfo of pageInfos.value) {
      for (const field of pageInfo.fields) {
        if ([3, 4, 8, 10].includes(field.fieldType)) {
          fieldIds.push(field.fieldId);
        }
      }
    }
    
    const fallbackData: ChoiceItemMap = {};
    fieldIds.forEach(id => {
      fallbackData[id] = [];
    });
    fieldChoiceMap.value = fallbackData;
  }
}

// base64解码函数
const decodeBase64 = (base64String: string): string => {
  try {
    // 检查是否为有效的base64字符串
    if (!base64String || typeof base64String !== 'string') {
      return base64String
    }
    
    // 尝试解码base64
    return atob(base64String)
  } catch (error) {
    console.warn('Base64解码失败:', error)
    // 如果解码失败，返回原始字符串
    return base64String
  }
}

// 获取字段值
const getFieldValue = (fieldId: number): any => {
  if (!itemData.value?.fields) return null
  
  const field = itemData.value.fields.find(f => f.fieldId === fieldId)
  return field?.value || null
}

// 获取字段显示值（处理base64解码）
const getFieldDisplayValue = (field: PageFieldVo): string => {
  const value = getFieldValue(field.fieldId)
  
  // 对于MULTILINE_EDIT_BOX字段类型，进行base64解码
  if (field.fieldType === FieldTypeEnum.MULTILINE_EDIT_BOX) {
    if (value && typeof value === 'string') {
      return decodeBase64(value)
    }
  }
  
  // 对于类型为3、4、8、10的字段，需要根据choiceId获取choiceName
  if ([3, 4, 8, 10].includes(field.fieldType)) {
    const fieldChoices = fieldChoiceMap.value[field.fieldId]
    if (fieldChoices && Array.isArray(value)) {
      // 多选情况
      return value.map(choiceId => {
        const choice = fieldChoices.find(c => c.choiceId === choiceId)
        return choice ? choice.choiceName : choiceId
      }).join(', ')
    } else if (fieldChoices && typeof value === 'number') {
      // 单选情况
      const choice = fieldChoices.find(c => c.choiceId === value)
      return choice ? choice.choiceName : value.toString()
    }
  }
  
  // 确保返回值是字符串类型，防止在FieldEditPopup中使用slice方法时出错
  return value ? value.toString() : ''
}

// base64编码函数
const encodeBase64 = (string: string): string => {
  try {
    if (!string || typeof string !== 'string') {
      return string
    }
    return btoa(string)
  } catch (error) {
    console.warn('Base64编码失败:', error)
    return string
  }
}

// 更新字段值
const updateFieldValue = (fieldId: number, value: any) => {
  console.log('updateFieldValue called, fieldId:', fieldId, 'value:', value);
  if (!itemData.value?.fields) return
  
  const fieldIndex = itemData.value.fields.findIndex(f => f.fieldId === fieldId)
  if (fieldIndex !== -1) {
    // 查找对应的PageFieldVo来获取字段类型
    let fieldType: number | undefined
    for (const pageInfo of pageInfos.value) {
      const pageField = pageInfo.fields.find(f => f.fieldId === fieldId)
      if (pageField) {
        fieldType = pageField.fieldType
        break
      }
    }
    
    // 创建新的字段对象
    const updatedField = { ...itemData.value.fields[fieldIndex] }
    
    // 对于MULTILINE_EDIT_BOX字段类型，保存时进行base64编码
    if (fieldType === FieldTypeEnum.MULTILINE_EDIT_BOX) {
      if (value && typeof value === 'string') {
        updatedField.value = encodeBase64(value)
      } else {
        updatedField.value = value
      }
    } 
    // 对于类型为3、4、8、10的字段，需要保存choiceId
    else if (fieldType && [3, 4, 8, 10].includes(fieldType)) {
      // 这些字段类型的值应该已经是choiceId了，直接保存
      updatedField.value = value
    } else {
      updatedField.value = value
    }
    
    console.log('updatedField:', updatedField);
    // 使用splice方法替换数组元素，以触发Vue响应式更新
    itemData.value.fields.splice(fieldIndex, 1, updatedField)
  }
}

// 编辑字段相关方法
const editField = async (field: PageFieldVo, event: any) => {
  event.stopPropagation();
  
  // 只有SHORT_TEXT类型的字段可以编辑
  if (field.fieldType === FieldTypeEnum.SHORT_TEXT) {
    editableFieldId.value = field.fieldId
    
    // 等待 DOM 更新后聚焦
    await nextTick()
    
    // 查找并聚焦到输入框
    const input = document.querySelector(`[data-field-id="${field.fieldId}"] input`) as HTMLInputElement | null
    if (input) {
      input.focus()
    }
  }
  // 弹出通用编辑弹窗
  else {
    currentEditField.value = field
    
    // 根据字段类型设置初始值和选项
    if ([FieldTypeEnum.PLAIN_TEXT, FieldTypeEnum.MULTILINE_EDIT_BOX].includes(field.fieldType)) {
      // 确保值是字符串类型
      const displayValue = getFieldDisplayValue(field)
      currentEditValue.value = typeof displayValue === 'string' ? displayValue : ''
      currentEditChoices.value = null
    } else if (field.fieldType === FieldTypeEnum.DATE_TIME_FIELD) {
      // 日期时间字段
      const fieldValue = getFieldValue(field.fieldId)
      currentEditValue.value = fieldValue || ''
      currentEditChoices.value = null
    } else if ([FieldTypeEnum.DROPDOWN_LIST, FieldTypeEnum.COMBOBOX].includes(field.fieldType)) {
      // 初始化选中值为choiceId
      const fieldValue = getFieldValue(field.fieldId)
      
      // 确保值是字符串或数字类型，避免对象传递
      if (fieldValue !== null && fieldValue !== undefined) {
        if (typeof fieldValue === 'string' || typeof fieldValue === 'number') {
          currentEditValue.value = fieldValue
        } else if (typeof fieldValue === 'object') {
          // 如果是对象，尝试获取choiceId
          currentEditValue.value = fieldValue.choiceId || fieldValue.id || ''
        } else {
          currentEditValue.value = String(fieldValue)
        }
      } else {
        currentEditValue.value = ''
      }
      
      // 设置选项
      const choices = fieldChoiceMap.value[field.fieldId]
      console.log('Setting choices for dropdown/combobox:', choices, 'FieldId:', field.fieldId);
      
      // 确保choices是数组且包含有效对象
      if (Array.isArray(choices) && choices.length > 0) {
        const validChoices = choices.filter(choice => 
          choice && 
          (typeof choice === 'object') && 
          choice.choiceId !== undefined && 
          choice.choiceName !== undefined
        ).map(choice => ({
          choiceId: choice.choiceId,
          choiceName: choice.choiceName
        }));
        currentEditChoices.value = validChoices;
        console.log('Valid choices for dropdown/combobox:', validChoices);
      } else {
        console.warn('Invalid or empty choices for dropdown/combobox:', choices);
        currentEditChoices.value = [];
      }
    } else if (field.fieldType === FieldTypeEnum.MULTIPLE_SELECTION_LISTBOX) {
      // 初始化选中值为choiceId数组
      const fieldValue = getFieldValue(field.fieldId)
      currentEditValue.value = Array.isArray(fieldValue) ? fieldValue : []
      
      // 设置选项
      const choices = fieldChoiceMap.value[field.fieldId]
      console.log('Setting choices for multiple selection:', choices, 'FieldId:', field.fieldId);
      
      // 确保choices是数组且包含有效对象
      if (Array.isArray(choices) && choices.length > 0) {
        const validChoices = choices.filter(choice => 
          choice && 
          (typeof choice === 'object') && 
          choice.choiceId !== undefined && 
          choice.choiceName !== undefined
        ).map(choice => ({
          choiceId: choice.choiceId,
          choiceName: choice.choiceName
        }));
        currentEditChoices.value = validChoices;
        console.log('Valid choices for multiple selection:', validChoices);
      } else {
        console.warn('Invalid or empty choices for multiple selection:', choices);
        currentEditChoices.value = [];
      }
    } else if (field.fieldType === FieldTypeEnum.CHECKBOX) {
      currentEditValue.value = getFieldValue(field.fieldId) || ''
      currentEditChoices.value = [{choiceId: '是', choiceName: '是'}, {choiceId: '否', choiceName: '否'}]
    } else if (field.fieldType === FieldTypeEnum.RADIO_BUTTON) {
      currentEditValue.value = getFieldValue(field.fieldId) || ''
      currentEditChoices.value = [{choiceId: '严重', choiceName: '严重'}, {choiceId: '高', choiceName: '高'}, {choiceId: '中', choiceName: '中'}, {choiceId: '低', choiceName: '低'}]
    } else if (field.fieldType === FieldTypeEnum.AMOUNT) {
      currentEditValue.value = getFieldValue(field.fieldId) || ''
      currentEditChoices.value = null
    } else {
      // 默认处理其他字段类型
      currentEditValue.value = getFieldValue(field.fieldId) || ''
      currentEditChoices.value = null
    }
    
    showFieldEditPopup.value = true
  }
}

const saveField = async (fieldId: number, value: any) => {
  console.log('保存字段:', fieldId, value)
  editableFieldId.value = null
  // 这里可以调用API保存字段值
}

// 保存字段值的通用方法
const saveFieldValue = () => {
  if (currentEditField.value) {
    updateFieldValue(currentEditField.value.fieldId, currentEditValue.value)
    saveField(currentEditField.value.fieldId, currentEditValue.value)
  }
  showFieldEditPopup.value = false
}

// 类型检查辅助函数
const isUserField = (field: PageFieldVo) => {
  // 根据字段类型判断是否为用户类型字段
  return field.fieldType === 0 && getFieldValue(field.fieldId) && typeof getFieldValue(field.fieldId) === 'object' && 'avatar' in getFieldValue(field.fieldId) && 'name' in getFieldValue(field.fieldId)
}

const isAttachmentField = (field: PageFieldVo) => {
  return field.fieldType === 17 && Array.isArray(getFieldValue(field.fieldId)) && getFieldValue(field.fieldId).length > 0 && getFieldValue(field.fieldId)[0] && typeof getFieldValue(field.fieldId)[0] === 'object' && 'name' in getFieldValue(field.fieldId)[0]
}

onMounted(() => {
  fetchPageSetting()
  fetchItemData()
})
</script>

<style scoped>
.custom-page {
  width: 100%;
}

.detail-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  margin: 15px;
  padding: 5px;
  overflow: hidden;
}

.field-value-vertical {
  width: 100%;
}

.field-value {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.field-value.editable-field {
  justify-content: flex-start;
  padding: 0;
  margin: 0;
}

.field-value-left {
  width: 100%;
  text-align: left;
}

.normal-value {
  color: #333;
  font-size: 14px;
  text-align: left;
}

.empty-value {
  color: #ccc;
  font-size: 14px;
  text-align: left;
}

.editable-field {
  padding: 6px 0;
  font-size: 14px;
  color: #333;
  width: 100%;
}

.editable-field :deep(.van-field__control) {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  text-align: left;
  padding: 6px 0;
}

.editable-field-container {
  width: 100%;
  padding: 6px 0;
  line-height: 1.5;
  box-sizing: border-box;
  text-align: left;
}



.vertical-cell {
  flex-direction: column;
  align-items: flex-start !important;
}

.vertical-cell :deep(.van-cell__title) {
  margin-bottom: 8px;
  width: 100%;
  color:#999;
}

.vertical-cell :deep(.van-cell__value) {
  width: 100%;
  text-align: left;
  margin-left: 0;
}

.consistent-height {
  min-height: 32px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}

/* 弹窗全屏布局样式 */
.full-height {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
  overflow: hidden;
}

.full-height-field {
  height: 100%;
}

.full-height-field :deep(.van-field__control) {
  height: 100%;
}

/* PLAIN_TEXT弹窗样式 */
.popup-container {
  width: 100vw;
  background-color: #fff;
}

.popup-content {
  padding: 16px;
}

.popup-content :deep(.van-field__word-limit) {
  text-align: right;
  margin-top: 4px;
}

.radio-group {
  width: 100%;
}

.radio-cell {
  display: flex;
  align-items: center;
  width: 100%;
}

.radio-label {
  margin-left: 12px;
  font-size: 14px;
  color: #333;
}

.checkbox-group {
  width: 100%;
}

.checkbox-cell {
  display: flex;
  align-items: center;
  width: 100%;
}

.checkbox-label {
  margin-left: 12px;
  font-size: 14px;
  color: #333;
}

/* AMOUNT弹窗样式 */
.amount-input-container {
  width: 100%;
}

.amount-display {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.currency-symbol {
  font-size: 24px;
  font-weight: 500;
  color: #333;
  margin-right: 8px;
}

.amount-value-display {
  font-size: 24px;
  font-weight: 500;
  color: #333;
  min-width: 100px;
  text-align: left;
}

.amount-value {
  color: #F56C6C;
  font-weight: 500;
}

/* 标签样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag {
  background-color: #f0f2f5;
  color: #666;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}


</style>
