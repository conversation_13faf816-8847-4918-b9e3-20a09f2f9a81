import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  { path: '/', redirect: '/login' },
  { path: '/login', component: () => import('@/views/Login.vue') },
  { path: '/home', component: () => import('@/views/home/<USER>') },
  { path: '/list', component: () => import('@/views/incident/list/index.vue') },
  { path: '/detail', component: () => import('@/views/incident/detail/index.vue') },
  { path: '/new', component: () => import('@/views/New.vue') },
  { path: '/profile', component: () => import('@/views/profile/index.vue') },

  { path: '/projects', component: () => import('@/views/project/index.vue') },
  { path: '/reports', component: () => import('@/views/report/index.vue') },
  { path: '/knowledge', component: () => import('@/views/Knowledge.vue') },
  
  // 报表页面路由
  { path: '/report/list', component: () => import('@/views/report/components/ListReport.vue') },
  { path: '/report/distribution', component: () => import('@/views/report/components/DistributionReport.vue') },
  { path: '/report/trend', component: () => import('@/views/report/components/TrendReport.vue') },

  { path: '/test', component: () => import('@/views/List.vue') },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_URL || '/'),
  routes,
})

export default router