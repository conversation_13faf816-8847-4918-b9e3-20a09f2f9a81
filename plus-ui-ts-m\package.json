{"name": "itsm-mobile-web", "version": "1.0.0", "description": "ITSM手机端Web应用", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "build:prod": "vue-tsc && vite build --mode production", "build:staging": "vue-tsc && vite build --mode staging", "preview": "vite preview", "preview:prod": "vite preview --mode production", "preview:preview": "vite preview --mode preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit", "analyze": "vite build --mode production && npx vite-bundle-analyzer dist"}, "dependencies": {"@vant/touch-emulator": "^1.4.0", "@vueuse/core": "^13.6.0", "await-to-js": "^3.0.0", "axios": "^1.6.0", "crypto-js": "^4.2.0", "echarts": "^6.0.0", "jsencrypt": "^3.3.2", "pinia": "^3.0.0", "vant": "^4.9.21", "vue": "^3.5.0", "vue-i18n": "^11.1.11", "vue-router": "^4.3.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/node": "^24.2.1", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-vue": "^5.0.0", "@vitest/ui": "^1.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.0", "@vue/tsconfig": "^0.5.0", "autoprefixer": "^10.4.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.0", "jsdom": "^23.0.0", "postcss": "^8.4.0", "postcss-px-to-viewport": "^1.1.1", "postcss-px-to-viewport-8-plugin": "^1.2.5", "sass": "^1.89.2", "typescript": "^5.3.0", "unplugin-auto-import": "^0.17.0", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.0", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^1.0.0", "vue-tsc": "^2.0.0"}}