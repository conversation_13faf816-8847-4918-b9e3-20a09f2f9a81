/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_API_BASE_URL: string
  readonly VITE_APP_BASE_API: string
  readonly VITE_DEBUG: string
  readonly VITE_ENABLE_PERFORMANCE_MONITORING: string
  readonly VITE_ENABLE_ERROR_REPORTING: string
  readonly VITE_ENABLE_USER_TRACKING: string
  readonly VITE_BUILD_OUTPUT_DIR: string
  readonly VITE_GENERATE_SOURCEMAP: string
  readonly VITE_CDN_BASE_URL: string
  readonly VITE_ENABLE_GZIP: string
  readonly VITE_ENABLE_BROTLI: string
  readonly VITE_ENABLE_IMAGE_COMPRESSION: string
  readonly VITE_ENABLE_CSS_CODE_SPLITTING: string
  readonly VITE_ENABLE_JS_CODE_SPLITTING: string
  readonly VITE_ENABLE_TREE_SHAKING: string
  readonly VITE_ENABLE_MINIFICATION: string
  readonly VITE_ENABLE_CHUNK_SIZE_WARNING: string
  readonly VITE_MAX_CHUNK_SIZE: string
  readonly VITE_ENABLE_PRELOAD: string
  readonly VITE_ENABLE_PREFETCH: string
  readonly VITE_APP_PORT: string
  readonly VITE_APP_ENCRYPT: string
  readonly VITE_APP_RSA_PUBLIC_KEY: string
  readonly VITE_APP_RSA_PRIVATE_KEY: string
  readonly VITE_APP_CLIENT_ID: string
  readonly VITE_APP_WEBSOCKET: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
} 