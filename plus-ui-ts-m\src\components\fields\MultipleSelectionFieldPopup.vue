<template>
  <FieldPopupWrapper
    v-model:show="show"
    :title="field?.fieldName || '多选选择'"
    position="bottom"
    round
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <van-checkbox-group v-model="localValue" class="checkbox-group">
      <van-cell-group>
        <van-cell 
          v-for="choice in choices" 
          :key="choice.choiceId" 
          clickable
        >
          <template #title>
            <van-checkbox :name="choice.choiceId">{{ choice.choiceName }}</van-checkbox>
          </template>
        </van-cell>
      </van-cell-group>
    </van-checkbox-group>
  </FieldPopupWrapper>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import type { PageFieldVo } from '@/api/field/types'
import FieldPopupWrapper from './FieldPopupWrapper.vue'

interface Props {
  show: boolean
  field: PageFieldVo
  modelValue: string[] | string | number
  choices: Array<{choiceId: number, choiceName: string}> | undefined
}

const props = defineProps<Props>()

const emit = defineEmits<{ 
  (e: 'update:show', value: boolean): void,
  (e: 'update:modelValue', value: (string | number)[]): void,
  (e: 'confirm', value: (string | number)[]): void,
  (e: 'cancel'): void
}>()

// 处理不同类型的modelValue
const getInitialValue = () => {
  if (Array.isArray(props.modelValue)) {
    return props.modelValue
  } else if (props.modelValue) {
    return [props.modelValue]
  }
  return []
}

const localValue = ref<(string | number)[]>(getInitialValue())

const show = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

watch(() => props.show, (newVal) => {
  if (newVal) {
    localValue.value = getInitialValue()
  }
})

const handleConfirm = () => {
  emit('confirm', localValue.value)
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.checkbox-group {
  margin-top: 16px;
}

.checkbox-group :deep(.van-checkbox) {
  flex: 1;
}

.checkbox-group :deep(.van-cell) {
  padding: 10px 16px;
}
</style>