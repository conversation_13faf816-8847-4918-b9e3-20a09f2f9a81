<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务目录 - ITSM系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="public/static/font/iconfont.css">
    <style>
        @font-face {
            font-family: 'AlibabaPuHuiTi';
            src: url('public/static/font/AlibabaPuHuiTi-3-45-Light.ttf') format('ttf');
            font-weight: 300;
            font-style: normal;
        }
        @font-face {
            font-family: 'AlibabaPuHuiTi';
            src: url('public/static/font/AlibabaPuHuiTi-3-55-Regular.ttf') format('ttf');
            font-weight: 400;
            font-style: normal;
        }
        @font-face {
            font-family: 'AlibabaPuHuiTi';
            src: url('public/static/font/AlibabaPuHuiTi-3-65-Medium.ttf') format('ttf');
            font-weight: 500;
            font-style: normal;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'AlibabaPuHuiTi', -apple-system, BlinkMacSystemFont, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background-color: #f5f7fa;
            width: 100%;
            max-width: 430px;
            margin: 0 auto;
            height: 100vh;
            overflow: hidden;
            position: relative;
            border-radius: 40px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        
        .app-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            background-color: white;
            border-radius: 40px;
        }
        
        .status-bar {
            height: 44px;
            width: 100%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            position: relative;
            z-index: 10;
            border-top-left-radius: 40px;
            border-top-right-radius: 40px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .tab-bar {
            height: 84px;
            width: 100%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-around;
            border-top: 1px solid rgba(0,0,0,0.05);
            padding-bottom: 20px;
            border-bottom-left-radius: 40px;
            border-bottom-right-radius: 40px;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 50px;
            color: #999;
        }
        
        .tab-item.active {
            color: #409EFF;
        }
        
        .content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 15px;
            background-color: #f5f7fa;
        }
        
        .service-category {
            background-color: white;
            margin-bottom: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        .service-category-header {
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
        }
        
        .service-category-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            margin-right: 12px;
        }
        
        .service-list {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            padding: 12px 0;
        }
        
        .service-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 8px;
            transition: all 0.2s;
        }
        
        .service-item:hover {
            background-color: #f9f9f9;
        }
        
        .service-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
        }
        
        .service-name {
            font-size: 12px;
            color: #333;
            text-align: center;
        }
        
        /* 新增的样式 */
        .category-nav {
            display: flex;
            overflow-x: auto;
            white-space: nowrap;
            padding: 10px 0;
            margin-bottom: 15px;
            scrollbar-width: none;
            -ms-overflow-style: none;
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        .category-nav::-webkit-scrollbar {
            display: none;
        }
        
        .category-nav-item {
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 16px;
            color: #666;
            font-size: 12px;
        }
        
        .category-nav-item.active {
            color: #409EFF;
            font-weight: 500;
            position: relative;
        }
        
        .category-nav-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 12px;
            height: 2px;
            background-color: #409EFF;
            border-radius: 2px;
        }
        
        .category-nav-icon {
            margin-bottom: 4px;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .subcategory {
            border-top: 1px solid #f0f0f0;
            padding: 0 16px;
        }
        
        .subcategory-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            cursor: pointer;
        }
        
        .subcategory-title {
            font-size: 14px;
            color: #333;
        }
        
        .subcategory-content {
            display: none;
        }
        
        .subcategory-content.active {
            display: block;
            padding-bottom: 12px;
        }
        
        .service-list-alt {
            display: flex;
            flex-direction: column;
        }
        
        .service-item-alt {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f5f5f5;
        }
        
        .service-item-alt:last-child {
            border-bottom: none;
        }
        
        .service-icon-alt {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .service-info {
            flex: 1;
        }
        
        .service-name-alt {
            font-size: 14px;
            color: #333;
            margin-bottom: 2px;
        }
        
        .service-desc {
            font-size: 12px;
            color: #999;
        }
        
        .frequent-services {
            background-color: white;
            margin-bottom: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            padding: 16px;
        }
        
        .frequent-services-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .frequent-services-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            margin-right: 12px;
            background-color: #e6f7ff;
            color: #1890ff;
        }
        
        .frequent-services-list {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
        }
        
        /* 折叠图标动画 */
        .toggle-icon {
            transition: transform 0.3s ease;
        }
        
        .toggle-icon.active {
            transform: rotate(180deg);
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="status-bar">
            <div class="flex items-center">
                <span class="text-black text-sm font-medium">9:41</span>
            </div>
            <div class="flex items-center space-x-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 10a6 6 0 0 0-12 0v8h12v-8z"/><path d="M20 10a8 8 0 0 0-16 0v8h2v-8a6 6 0 0 1 12 0v8h2v-8z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6.33 20.855A10.968 10.968 0 0 1 2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10c-1.97 0-3.823-.518-5.425-1.428"/><path d="M16 12V7"/><path d="M8 12v5"/><path d="M12 16v3"/><path d="M12 7v5"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12a10 10 0 0 1 10 -10v0a10 10 0 0 1 10 10v0a10 10 0 0 1 -10 10h-2a8 8 0 0 0 -8 -8"/><path d="M5 18v-2"/><path d="M2 6v-2h2"/><path d="M20 6v-2h-2"/><path d="M3 10h2"/><path d="M17 14h4"/></svg>
            </div>
        </div>
        
        <div class="flex items-center justify-between bg-white px-4 py-3">
            <h1 class="text-xl font-bold">服务目录</h1>
            <div class="flex items-center space-x-3">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-600"><polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon></svg>
            </div>
        </div>
        
        <div class="content">
            <!-- 搜索框 -->
            <div class="relative mb-4">
                <input type="text" placeholder="搜索服务..." class="w-full bg-white rounded-lg border border-gray-200 pl-10 pr-4 py-2 text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="absolute left-3 top-2.5 text-gray-400"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
            </div>
            
            <!-- 分类导航 -->
            <div class="category-nav mb-4">
                <a href="#frequent" class="category-nav-item active">
                    <div class="category-nav-icon text-blue-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 20V10"></path><path d="m18 14-6-6-6 6"></path><path d="M12 4v2"></path></svg>
                    </div>
                    <span>常用</span>
                </a>
                <a href="#user" class="category-nav-item">
                    <div class="category-nav-icon text-blue-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                    </div>
                    <span>用户支持</span>
                </a>
                <a href="#infra" class="category-nav-item">
                    <div class="category-nav-icon text-orange-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect><rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect><line x1="6" y1="6" x2="6.01" y2="6"></line><line x1="6" y1="18" x2="6.01" y2="18"></line></svg>
                    </div>
                    <span>基础架构</span>
                </a>
                <a href="#sap" class="category-nav-item">
                    <div class="category-nav-icon text-cyan-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="4" y="4" width="16" height="16" rx="2" ry="2"></rect><rect x="9" y="9" width="6" height="6"></rect><line x1="9" y1="1" x2="9" y2="4"></line><line x1="15" y1="1" x2="15" y2="4"></line><line x1="9" y1="20" x2="9" y2="23"></line><line x1="15" y1="20" x2="15" y2="23"></line><line x1="20" y1="9" x2="23" y2="9"></line><line x1="20" y1="14" x2="23" y2="14"></line><line x1="1" y1="9" x2="4" y2="9"></line><line x1="1" y1="14" x2="4" y2="14"></line></svg>
                    </div>
                    <span>SAP系统</span>
                </a>
                <a href="#other" class="category-nav-item">
                    <div class="category-nav-icon text-gray-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>
                    </div>
                    <span>其他</span>
                </a>
            </div>
            
            <!-- 常用服务分类 -->
            <div id="frequent" class="frequent-services">
                <div class="frequent-services-header">
                    <div class="frequent-services-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline></svg>
                    </div>
                    <h2 class="text-base font-medium">常用服务</h2>
                </div>
                <div class="frequent-services-list">
                    <a href="create-work-order.html" class="service-item">
                        <div class="service-icon bg-blue-50 text-blue-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
                        </div>
                        <span class="service-name">OA-账户申请</span>
                    </a>
                    <a href="create-work-order.html" class="service-item">
                        <div class="service-icon bg-orange-50 text-orange-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                        </div>
                        <span class="service-name">OA-系统故障</span>
                    </a>
                    <a href="create-work-order.html" class="service-item">
                        <div class="service-icon bg-green-50 text-green-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 9V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v3"></path><path d="M3 16c0 2 2 3 4 3h10c2 0 4-1 4-3"></path><line x1="12" y1="19" x2="12" y2="9"></line><line x1="8" y1="13" x2="16" y2="13"></line></svg>
                        </div>
                        <span class="service-name">OA-权限变更</span>
                    </a>
                    <a href="create-work-order.html" class="service-item">
                        <div class="service-icon bg-red-50 text-red-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon></svg>
                        </div>
                        <span class="service-name">OA-流程停止</span>
                    </a>
                </div>
            </div>
            
            <!-- 用户支持分类 -->
            <div id="user" class="service-category">
                <div class="service-category-header">
                    <div class="service-category-icon bg-blue-100 text-blue-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                    </div>
                    <h2 class="text-base font-medium">用户支持</h2>
                </div>
                
                <!-- 子分类：账户管理 -->
                <div class="subcategory">
                    <div class="subcategory-header" onclick="toggleSubcategory(this)">
                        <span class="subcategory-title">账户管理</span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="toggle-icon"><polyline points="6 9 12 15 18 9"></polyline></svg>
                    </div>
                    <div class="subcategory-content active">
                        <div class="service-list-alt">
                            <a href="create-work-order.html" class="service-item-alt">
                                <div class="service-icon-alt bg-blue-50 text-blue-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline></svg>
                                </div>
                                <div class="service-info">
                                    <div class="service-name-alt">OA-账户申请</div>
                                    <div class="service-desc">申请新的OA系统账户</div>
                                </div>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
                            </a>
                            <a href="create-work-order.html" class="service-item-alt">
                                <div class="service-icon-alt bg-green-50 text-green-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 9V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v3"></path><path d="M3 16c0 2 2 3 4 3h10c2 0 4-1 4-3"></path></svg>
                                </div>
                                <div class="service-info">
                                    <div class="service-name-alt">OA-权限变更</div>
                                    <div class="service-desc">申请修改OA系统权限</div>
                                </div>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- 子分类：功能需求 -->
                <div class="subcategory">
                    <div class="subcategory-header" onclick="toggleSubcategory(this)">
                        <span class="subcategory-title">功能需求</span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="toggle-icon"><polyline points="6 9 12 15 18 9"></polyline></svg>
                    </div>
                    <div class="subcategory-content">
                        <div class="service-list-alt">
                            <a href="create-work-order.html" class="service-item-alt">
                                <div class="service-icon-alt bg-purple-50 text-purple-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg>
                                </div>
                                <div class="service-info">
                                    <div class="service-name-alt">OA-新增需求</div>
                                    <div class="service-desc">提交OA系统新功能需求</div>
                                </div>
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#999" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 基础架构分类 -->
            <div id="infra" class="service-category">
                <div class="service-category-header">
                    <div class="service-category-icon bg-orange-100 text-orange-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect><rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect><line x1="6" y1="6" x2="6.01" y2="6"></line><line x1="6" y1="18" x2="6.01" y2="18"></line></svg>
                    </div>
                    <h2 class="text-base font-medium">基础架构</h2>
                </div>
                <div class="service-list">
                    <a href="create-work-order.html" class="service-item">
                        <div class="service-icon bg-orange-50 text-orange-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                        </div>
                        <span class="service-name">OA-系统故障</span>
                    </a>
                    <a href="create-work-order.html" class="service-item">
                        <div class="service-icon bg-red-50 text-red-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon></svg>
                        </div>
                        <span class="service-name">OA-流程停止</span>
                    </a>
                    <a href="create-work-order.html" class="service-item">
                        <div class="service-icon bg-yellow-50 text-yellow-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M23 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>
                        </div>
                        <span class="service-name">OA-流程修订</span>
                    </a>
                </div>
            </div>
            
            <!-- SAP系统分类 -->
            <div id="sap" class="service-category">
                <div class="service-category-header">
                    <div class="service-category-icon bg-cyan-100 text-cyan-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="4" y="4" width="16" height="16" rx="2" ry="2"></rect><rect x="9" y="9" width="6" height="6"></rect><line x1="9" y1="1" x2="9" y2="4"></line><line x1="15" y1="1" x2="15" y2="4"></line><line x1="9" y1="20" x2="9" y2="23"></line><line x1="15" y1="20" x2="15" y2="23"></line><line x1="20" y1="9" x2="23" y2="9"></line><line x1="20" y1="14" x2="23" y2="14"></line><line x1="1" y1="9" x2="4" y2="9"></line><line x1="1" y1="14" x2="4" y2="14"></line></svg>
                    </div>
                    <h2 class="text-base font-medium">SAP系统</h2>
                </div>
                <div class="service-list">
                    <a href="create-work-order.html" class="service-item">
                        <div class="service-icon bg-cyan-50 text-cyan-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path><circle cx="12" cy="12" r="3"></circle></svg>
                        </div>
                        <span class="service-name">OA-流程制定</span>
                    </a>
                    <a href="create-work-order.html" class="service-item">
                        <div class="service-icon bg-indigo-50 text-indigo-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path></svg>
                        </div>
                        <span class="service-name">OA-集成接口</span>
                    </a>
                </div>
            </div>
            
            <!-- 其他分类 -->
            <div id="other" class="service-category">
                <div class="service-category-header">
                    <div class="service-category-icon bg-gray-100 text-gray-500">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>
                    </div>
                    <h2 class="text-base font-medium">其他</h2>
                </div>
                <div class="service-list">
                    <a href="create-work-order.html" class="service-item">
                        <div class="service-icon bg-gray-50 text-gray-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg>
                        </div>
                        <span class="service-name">其他问题</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 用户导航栏 -->
        <div id="userTabBar" class="tab-bar">
            <a href="home.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="service-catalog.html" class="tab-item active">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line></svg>
                <span class="text-xs mt-1">服务目录</span>
            </a>
            <a href="work-order.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline></svg>
                <span class="text-xs mt-1">工单</span>
            </a>
            <a href="knowledge.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path><path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path></svg>
                <span class="text-xs mt-1">知识库</span>
            </a>
            <a href="profile.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
    
    <script>
        // 根据URL参数判断是否是工程师角色
        function checkUserRole() {
            const urlParams = new URLSearchParams(window.location.search);
            const role = urlParams.get('role');
            
            if (role === 'engineer') {
                // 如果是工程师角色，重定向到首页
                window.location.href = 'home.html?role=engineer';
            }
        }
        
        // 切换子分类显示/隐藏
        function toggleSubcategory(element) {
            const content = element.nextElementSibling;
            const icon = element.querySelector('.toggle-icon');
            
            content.classList.toggle('active');
            icon.classList.toggle('active');
        }
        
        // 分类导航点击事件
        document.querySelectorAll('.category-nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                // 阻止默认行为
                e.preventDefault();
                
                // 移除所有激活状态
                document.querySelectorAll('.category-nav-item').forEach(i => {
                    i.classList.remove('active');
                });
                
                // 添加当前激活状态
                this.classList.add('active');
                
                // 获取目标分类ID
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                // 滚动到目标位置
                if (targetElement) {
                    const content = document.querySelector('.content');
                    content.scrollTo({
                        top: targetElement.offsetTop - 10,
                        behavior: 'smooth'
                    });
                }
            });
        });
        
        // 实现搜索过滤功能
        const searchInput = document.querySelector('input[placeholder="搜索服务..."]');
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();
            
            // 搜索所有服务项
            document.querySelectorAll('.service-item, .service-item-alt').forEach(item => {
                const serviceName = item.querySelector('.service-name, .service-name-alt').textContent.toLowerCase();
                const serviceDesc = item.querySelector('.service-desc')?.textContent.toLowerCase() || '';
                
                if (serviceName.includes(searchTerm) || serviceDesc.includes(searchTerm)) {
                    item.style.display = '';
                    
                    // 如果在子分类中，展开该子分类
                    const subcategory = item.closest('.subcategory-content');
                    if (subcategory && !subcategory.classList.contains('active')) {
                        subcategory.classList.add('active');
                        subcategory.previousElementSibling.querySelector('.toggle-icon').classList.add('active');
                    }
                } else {
                    item.style.display = 'none';
                }
            });
            
            // 处理空结果的子分类
            document.querySelectorAll('.subcategory-content').forEach(content => {
                const visibleItems = Array.from(content.querySelectorAll('.service-item-alt')).filter(item => item.style.display !== 'none');
                
                if (visibleItems.length === 0 && searchTerm !== '') {
                    content.closest('.subcategory').style.display = 'none';
                } else {
                    content.closest('.subcategory').style.display = '';
                }
            });
            
            // 处理空结果的分类
            document.querySelectorAll('.service-category').forEach(category => {
                const visibleSubcategories = Array.from(category.querySelectorAll('.subcategory')).filter(sub => sub.style.display !== 'none');
                const visibleItems = Array.from(category.querySelectorAll('.service-item')).filter(item => item.style.display !== 'none');
                
                if (visibleSubcategories.length === 0 && visibleItems.length === 0 && searchTerm !== '') {
                    category.style.display = 'none';
                } else {
                    category.style.display = '';
                }
            });
        });
        
        // 页面加载时检查角色
        window.addEventListener('DOMContentLoaded', function() {
            checkUserRole();
            
            // 默认打开第一个子分类
            document.querySelectorAll('.subcategory-content')[0].classList.add('active');
        });
    </script>
</body>
</html> 