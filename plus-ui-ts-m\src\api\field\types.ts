export interface ChoiceItem {
  fieldId: number;
  choiceId: number;
  choiceName: string;
  isActive?: number;
  choiceOrder?: number;
}

export interface PageSettingVo {
  pageFields: PageFieldVo[];
  pages: Page[];
  pageActions: PageActionVo[];
};

export interface PageActionVo {
  typeId: number;
  typeName: string;
  itemTypePageActions: ItemTypePageActionVo[];
}

export interface ItemTypePageActionVo {
  pageId: number;
  actionId: number;
  pageOrder: number;
};

export interface Page {
  pageId: number;
  pageName: string;
  moduleId: number;
  fields: PageRowField[];
}

export interface PageFieldVo {
  pageId: number;
  pageName: string;
  fieldId: number;
  fieldName: string;
  fieldType: number;
  fieldSubtype: number;
  pageRow: number;
  pageColumn: number;
  moduleId: number;
  ifDisabled: boolean;
  ifMandatory: boolean;
  ifVisible: boolean;
  formula: string;
  parentFieldId?: number;
  childFieldIds?: number[];
}

export interface PageRowField {
  row: number;
  ColumnFields: FieldItem[];
}

export interface FieldItem {
  fieldId: number;
  defaultName: string;
  fieldName: string;
  fieldTypeId: number;
  fieldSubtype: number;
  ifMandatory: boolean;
  ifVisible: boolean;
  ifDisabled: boolean;
  ColumnId: number;
  listChoice?: ChoiceItem[];
  additionInfo?: any[];
  tableData?: any[];
  attributes?: FieldAttributes;
  parentFieldId?: number;
  childFieldIds?: number[];
}

export interface FieldAttributes {
  supportHTMLFormat: boolean;
  ifMandatory: boolean;
  ifDisabled: boolean;
  ifVisible: boolean;
  formula?: string;
  rules?: any;
  canUploadAttachment?: boolean;
  canDeleteAttachment?: boolean;
}

export interface FieldChoiceBo {
  projectId: string | number;
  fieldIds: number[];
  allowEmpty?: boolean;
}

export interface ChoiceItem {
  fieldId: number;
  choiceId: number;
  choiceName: string;
  isActive?: number;
  choiceOrder?: number;
}
export type ChoiceItemMap = Record<number, ChoiceItem[]>;