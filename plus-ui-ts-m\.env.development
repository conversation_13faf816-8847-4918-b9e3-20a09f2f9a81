﻿# 开发环境配置
# NODE_ENV=development  # Vite 会自动设置，不需要在 .env 文件中设置

# 应用标题
VITE_APP_TITLE=ITSM移动端(开发环境)

# API基础URL - 开发环境API地址
VITE_API_BASE_URL=

# 开发环境
VITE_APP_BASE_API = '/dev-api'
# 应用基础路径
VITE_BASE_URL= '/'

# 应用访问路径 例如使用前缀 /admin/
VITE_APP_CONTEXT_PATH = '/'
# 是否启用调试模式
VITE_DEBUG=true

# 是否启用性能监控
VITE_ENABLE_PERFORMANCE_MONITORING=false

# 是否启用错误上报
VITE_ENABLE_ERROR_REPORTING=false

# 是否启用用户行为追踪
VITE_ENABLE_USER_TRACKING=false

# 构建输出目录
VITE_BUILD_OUTPUT_DIR=dist

# 是否生成sourcemap
VITE_GENERATE_SOURCEMAP=true

# CDN基础URL（开发环境通常不需要）
VITE_CDN_BASE_URL=

# 是否启用gzip压缩
VITE_ENABLE_GZIP=false

# 是否启用brotli压缩
VITE_ENABLE_BROTLI=false

# 是否启用图片压缩
VITE_ENABLE_IMAGE_COMPRESSION=false

# 是否启用CSS代码分割
VITE_ENABLE_CSS_CODE_SPLITTING=false

# 是否启用JS代码分割
VITE_ENABLE_JS_CODE_SPLITTING=false

# 是否启用tree shaking
VITE_ENABLE_TREE_SHAKING=true

# 是否启用minification
VITE_ENABLE_MINIFICATION=false

# 是否启用chunk大小警告
VITE_ENABLE_CHUNK_SIZE_WARNING=false

# 最大chunk大小（KB）
VITE_MAX_CHUNK_SIZE=1000

# 是否启用预加载
VITE_ENABLE_PRELOAD=false

# 是否启用预取
VITE_ENABLE_PREFETCH=false

# 开发环境端口
VITE_APP_PORT = 3000

# 接口加密功能开关(开发环境通常关闭)
VITE_APP_ENCRYPT = true

# 接口加密传输 RSA 公钥与后端解密私钥对应 如更换需前后端一同更换
VITE_APP_RSA_PUBLIC_KEY = 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ=='

# 接口响应解密 RSA 私钥与后端加密公钥对应 如更换需前后端一同更换
VITE_APP_RSA_PRIVATE_KEY = 'MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAmc3CuPiGL/LcIIm7zryCEIbl1SPzBkr75E2VMtxegyZ1lYRD+7TZGAPkvIsBcaMs6Nsy0L78n2qh+lIZMpLH8wIDAQABAkEAk82Mhz0tlv6IVCyIcw/s3f0E+WLmtPFyR9/WtV3Y5aaejUkU60JpX4m5xNR2VaqOLTZAYjW8Wy0aXr3zYIhhQQIhAMfqR9oFdYw1J9SsNc+CrhugAvKTi0+BF6VoL6psWhvbAiEAxPPNTmrkmrXwdm/pQQu3UOQmc2vCZ5tiKpW10CgJi8kCIFGkL6utxw93Ncj4exE/gPLvKcT+1Emnoox+O9kRXss5AiAMtYLJDaLEzPrAWcZeeSgSIzbL+ecokmFKSDDcRske6QIgSMkHedwND1olF8vlKsJUGK3BcdtM8w4Xq7BpSBwsloE='

# 客户端id
VITE_APP_CLIENT_ID = 'e5cd7e4891bf95d1d19206ce24a7b32e'

# websocket 开关 开发环境默认关闭，使用sse推送
VITE_APP_WEBSOCKET = false
