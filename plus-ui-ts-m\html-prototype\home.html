<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - ITSM系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background-color: #f5f7fa;
            width: 100%;
            max-width: 430px;
            margin: 0 auto;
            height: 100vh;
            overflow: hidden;
            position: relative;
        }
        
        .app-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            background-color: white;
        }
        
        .status-bar {
            height: 44px;
            width: 100%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            position: relative;
            z-index: 10;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .tab-bar {
            height: 60px;
            width: 100%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-around;
            border-top: 1px solid rgba(0,0,0,0.05);
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 50px;
            color: #999;
            position: relative;
        }
        
        .tab-item.active {
            color: #409EFF;
        }
        
        .badge-count {
            position: absolute;
            top: -2px;
            right: -2px;
            background-color: #F56C6C;
            color: white;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 15px;
            background-color: #f5f7fa;
        }
        
        .card {
            background-color: #fff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 15px;
        }
        
        .module-item {
            display: flex;
            align-items: center;
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            position: relative;
        }
        
        .module-item:last-child {
            border-bottom: none;
        }
        
        .module-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            padding: 15px;
            background-color: #fff;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .avatar {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
            margin-right: 12px;
            position: relative;
            overflow: hidden;
        }
        
        .avatar::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(
                to bottom right,
                rgba(255, 255, 255, 0.3),
                rgba(255, 255, 255, 0)
            );
            transform: rotate(30deg);
        }
        
        .announcement-item {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .announcement-item:last-child {
            border-bottom: none;
        }
        
        .announcement-title {
            font-weight: 500;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }
        
        .announcement-date {
            font-size: 12px;
            color: #999;
            margin-left: 8px;
        }
        
        .announcement-content {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }
        
        .important {
            color: #F56C6C;
            font-weight: bold;
        }
        
        .section-header {
            padding: 0 15px 10px;
            font-weight: 500;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="status-bar">
            <h1 class="text-lg font-bold">ITSM系统</h1>
        </div>
        
        <!-- 用户信息 -->
        <div class="user-info">
            <div class="avatar">张</div>
            <div>
                <h2 class="text-lg font-bold">张三</h2>
                <p class="text-sm text-gray-500">IT支持部 | 工程师</p>
            </div>
        </div>
        
        <div class="content">
            <!-- 模块菜单 -->
            <div class="card">
                <div class="section-header">模块清单</div>
                <div class="module-item">
                    <div class="module-icon" style="background-color: rgba(64, 158, 255, 0.1);">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#409EFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>
                    </div>
                    <div style="flex: 1;">
                        <h3 class="font-medium">事件管理</h3>
                        <p class="text-sm text-gray-500">处理服务请求和故障</p>
                    </div>
                    <span class="badge-count">5</span>
                </div>
                
                <div class="module-item">
                    <div class="module-icon" style="background-color: rgba(230, 162, 60, 0.1);">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#E6A23C" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path><line x1="12" y1="9" x2="12" y2="13"></line><line x1="12" y1="17" x2="12.01" y2="17"></line></svg>
                    </div>
                    <div style="flex: 1;">
                        <h3 class="font-medium">问题管理</h3>
                        <p class="text-sm text-gray-500">调查和解决根本原因</p>
                    </div>
                    <span class="badge-count">3</span>
                </div>
                
                <div class="module-item">
                    <div class="module-icon" style="background-color: rgba(103, 194, 58, 0.1);">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#67C23A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="4 17 10 11 4 5"></polyline><line x1="12" y1="19" x2="20" y2="19"></line></svg>
                    </div>
                    <div style="flex: 1;">
                        <h3 class="font-medium">变更管理</h3>
                        <p class="text-sm text-gray-500">控制IT环境的变更</p>
                    </div>
                    <span class="badge-count">2</span>
                </div>
                
                <div class="module-item">
                    <div class="module-icon" style="background-color: rgba(144, 147, 153, 0.1);">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#909399" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>
                    </div>
                    <div style="flex: 1;">
                        <h3 class="font-medium">报表统计</h3>
                        <p class="text-sm text-gray-500">查看系统运行数据</p>
                    </div>
                </div>
                
                <div class="module-item">
                    <div class="module-icon" style="background-color: rgba(151, 117, 250, 0.1);">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#9775FA" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path><path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path></svg>
                    </div>
                    <div style="flex: 1;">
                        <h3 class="font-medium">知识库</h3>
                        <p class="text-sm text-gray-500">解决方案和文档中心</p>
                    </div>
                </div>
            </div>
            
            <!-- 公告区域 -->
            <div class="mb-4">
                <div class="section-header">系统公告</div>
                <div class="card">
                    <div class="announcement-item">
                        <div class="announcement-title">
                            <span class="important">[重要]</span> 系统维护通知
                            <span class="announcement-date">2023-04-25</span>
                        </div>
                        <div class="announcement-content">
                            本系统将于2023年4月28日00:00-06:00进行系统维护升级，期间将暂停服务，请提前做好工作安排。
                        </div>
                    </div>
                    
                    <div class="announcement-item">
                        <div class="announcement-title">
                            IT服务管理流程更新
                            <span class="announcement-date">2023-04-24</span>
                        </div>
                        <div class="announcement-content">
                            根据最新IT服务管理规范，我们对事件管理流程进行了优化，新增了自动分类功能，请及时查看知识库中的更新说明。
                        </div>
                    </div>
                    
                    <div class="announcement-item">
                        <div class="announcement-title">
                            新功能上线：知识库评分
                            <span class="announcement-date">2023-04-22</span>
                        </div>
                        <div class="announcement-content">
                            知识库新增了解决方案评分功能，您可以在查看解决方案后对其进行评分，帮助我们持续改进服务质量。
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <a href="home.html" class="tab-item active">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="modules.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="7" height="7"></rect><rect x="14" y="3" width="7" height="7"></rect><rect x="14" y="14" width="7" height="7"></rect><rect x="3" y="14" width="7" height="7"></rect></svg>
                <span class="text-xs mt-1">模块</span>
            </a>
            <a href="reports.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="20" x2="12" y2="10"></line><line x1="18" y1="20" x2="18" y2="4"></line><line x1="6" y1="20" x2="6" y2="16"></line></svg>
                <span class="text-xs mt-1">报表</span>
            </a>
            <a href="profile.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
</body>
</html>
