<template>
    <div class="app-container">
      <!-- 顶部状态栏 -->
      <div class="status-bar">
        <h1 class="status-title">报表中心</h1>
      </div>
  
      <!-- 内容区域 -->
      <ReportContent :project-id="projectId" />
  
  <!-- 底部导航栏 -->
      <van-tabbar route fixed>
        <van-tabbar-item to="/home" icon="home-o">首页</van-tabbar-item>
        <van-tabbar-item to="/projects" icon="apps-o">项目</van-tabbar-item>
        <van-tabbar-item to="/reports" icon="chart-trending-o">报表</van-tabbar-item>
        <van-tabbar-item to="/profile" icon="user-o">我的</van-tabbar-item>
      </van-tabbar>
    </div>
  </template>
  
  <script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import ReportContent from '@/components/ReportContent.vue'

const router = useRouter()

// 这里可以从路由参数或store中获取projectId
// 暂时使用默认值，实际使用时应该从路由参数或用户状态中获取
const projectId = ref(1)

const switchTab = (path: string) => {
  router.push(path)
}
</script>
  
  <style scoped>
.app-container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: white;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
  -webkit-tap-highlight-color: transparent;
}

.status-bar {
  height: 44px;
  width: 100%;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  position: relative;
  z-index: 10;
  border-bottom: 1px solid rgba(0,0,0,0.05);
}

.status-title {
  font-size: 16px;
  font-weight: bold;
}
</style>