<template>
  <div class="location-field">
    <div class="location-info">
      <van-icon name="location" />
      <span>{{ displayValue }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { PageFieldVo } from '@/api/field/types'

interface Props {
  field: PageFieldVo
  fieldValue: any
}

const props = defineProps<Props>()

const displayValue = computed(() => {
  if (!props.fieldValue) return ''
  const { address } = props.fieldValue
  return address || ''
})
</script>

<style scoped>
.location-info {
  display: flex;
  align-items: center;
}

.location-info span {
  margin-left: 4px;
}
</style>