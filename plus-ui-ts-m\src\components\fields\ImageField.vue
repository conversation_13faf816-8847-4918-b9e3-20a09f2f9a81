<template>
  <div class="image-field">
    <van-image
      v-for="(image, index) in images"
      :key="index"
      :src="image"
      :width="100"
      :height="100"
      fit="cover"
      @click="previewImage(index)"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ImagePreview } from 'vant'
import type { PageFieldVo } from '@/api/field/types'

interface Props {
  field: PageFieldVo
  fieldValue: any
}

const props = defineProps<Props>()

const images = computed(() => {
  if (Array.isArray(props.fieldValue)) {
    return props.fieldValue
  }
  return []
})

const previewImage = (index: number) => {
  ImagePreview({
    images: images.value,
    startPosition: index
  })
}
</script>

<style scoped>
.image-field {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
</style>