<template>
  <div class="user-info">
    <img class="avatar-small" :src="fieldValue?.avatar" />
    <span>{{ fieldValue?.name }}</span>
  </div>
</template>

<script setup lang="ts">
import type { PageFieldVo } from '@/api/field/types'

interface Props {
  field: PageFieldVo
  fieldValue: any
}

defineProps<Props>()
</script>

<style scoped>
.user-info {
  display: flex;
  align-items: center;
}

.avatar-small {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 8px;
}
</style>