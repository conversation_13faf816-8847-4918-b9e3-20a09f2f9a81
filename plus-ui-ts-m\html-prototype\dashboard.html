<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据看板 - ITSM系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="public/static/font/iconfont.css">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        @font-face {
            font-family: 'AlibabaPuHuiTi';
            src: url('public/static/font/AlibabaPuHuiTi-3-45-Light.ttf') format('ttf');
            font-weight: 300;
            font-style: normal;
        }
        @font-face {
            font-family: 'AlibabaPuHuiTi';
            src: url('public/static/font/AlibabaPuHuiTi-3-55-Regular.ttf') format('ttf');
            font-weight: 400;
            font-style: normal;
        }
        @font-face {
            font-family: 'AlibabaPuHuiTi';
            src: url('public/static/font/AlibabaPuHuiTi-3-65-Medium.ttf') format('ttf');
            font-weight: 500;
            font-style: normal;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'AlibabaPuHuiTi', -apple-system, BlinkMacSystemFont, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background-color: #f5f7fa;
            width: 100%;
            max-width: 430px;
            margin: 0 auto;
            height: 100vh;
            overflow: hidden;
            position: relative;
            border-radius: 40px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        
        .app-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            background-color: white;
            border-radius: 40px;
        }
        
        .status-bar {
            height: 44px;
            width: 100%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            position: relative;
            z-index: 10;
            border-top-left-radius: 40px;
            border-top-right-radius: 40px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .tab-bar {
            height: 84px;
            width: 100%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-around;
            border-top: 1px solid rgba(0,0,0,0.05);
            padding-bottom: 20px;
            border-bottom-left-radius: 40px;
            border-bottom-right-radius: 40px;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 50px;
            color: #999;
        }
        
        .tab-item.active {
            color: #409EFF;
        }
        
        .content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 15px;
            background-color: #f5f7fa;
        }
        
        .card {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .badge-primary {
            background-color: rgba(64, 158, 255, 0.1);
            color: #409EFF;
        }
        
        .badge-warning {
            background-color: rgba(230, 162, 60, 0.1);
            color: #E6A23C;
        }
        
        .badge-danger {
            background-color: rgba(245, 108, 108, 0.1);
            color: #F56C6C;
        }
        
        .badge-success {
            background-color: rgba(103, 194, 58, 0.1);
            color: #67C23A;
        }
        
        .btn-primary {
            background-color: #409EFF;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 500;
            font-size: 16px;
            text-align: center;
            width: 100%;
            display: block;
        }
        
        .btn-outline {
            background-color: transparent;
            color: #409EFF;
            border: 1px solid #409EFF;
            border-radius: 8px;
            padding: 12px 20px;
            font-weight: 500;
            font-size: 16px;
            text-align: center;
            width: 100%;
            display: block;
        }
        
        .stat-card {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 15px 10px;
            text-align: center;
            border-right: 1px solid rgba(0,0,0,0.05);
        }
        
        .stat-card:last-child {
            border-right: none;
        }
        
        .tab-buttons {
            display: flex;
            border-radius: 8px;
            overflow: hidden;
            background-color: #f0f2f5;
            padding: 3px;
        }
        
        .tab-button {
            flex: 1;
            text-align: center;
            padding: 8px 0;
            font-size: 14px;
            border-radius: 6px;
            transition: all 0.3s;
        }
        
        .tab-button.active {
            background-color: #fff;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
            color: #409EFF;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="status-bar">
            <div class="flex items-center">
                <span class="text-black text-sm font-medium">9:41</span>
            </div>
            <div class="flex items-center space-x-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 10a6 6 0 0 0-12 0v8h12v-8z"/><path d="M20 10a8 8 0 0 0-16 0v8h2v-8a6 6 0 0 1 12 0v8h2v-8z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6.33 20.855A10.968 10.968 0 0 1 2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10c-1.97 0-3.823-.518-5.425-1.428"/><path d="M16 12V7"/><path d="M8 12v5"/><path d="M12 16v3"/><path d="M12 7v5"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12a10 10 0 0 1 10 -10v0a10 10 0 0 1 10 10v0a10 10 0 0 1 -10 10h-2a8 8 0 0 0 -8 -8"/><path d="M5 18v-2"/><path d="M2 6v-2h2"/><path d="M20 6v-2h-2"/><path d="M3 10h2"/><path d="M17 14h4"/></svg>
            </div>
        </div>
        
        <div class="flex items-center justify-between bg-white px-4 py-3">
            <h1 class="text-xl font-bold">数据看板</h1>
            <div class="flex items-center space-x-3">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-600"><rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path></svg>
            </div>
        </div>
        
        <div class="content">
            <!-- 日期选择器 -->
            <div class="flex items-center justify-between mb-4">
                <div class="text-gray-500 text-sm">数据时间范围</div>
                <div class="flex items-center bg-white px-3 py-1.5 rounded-lg">
                    <span class="text-sm font-medium">最近30天</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><polyline points="6 9 12 15 18 9"></polyline></svg>
                </div>
            </div>
            
            <!-- 统计数据卡片 -->
            <div class="card mb-4">
                <div class="grid grid-cols-4">
                    <div class="stat-card">
                        <div class="text-gray-500 text-xs mb-1">工单总数</div>
                        <div class="text-xl font-bold">328</div>
                    </div>
                    <div class="stat-card">
                        <div class="text-gray-500 text-xs mb-1">待处理</div>
                        <div class="text-xl font-bold">42</div>
                    </div>
                    <div class="stat-card">
                        <div class="text-gray-500 text-xs mb-1">处理中</div>
                        <div class="text-xl font-bold">87</div>
                    </div>
                    <div class="stat-card">
                        <div class="text-gray-500 text-xs mb-1">已完成</div>
                        <div class="text-xl font-bold">199</div>
                    </div>
                </div>
            </div>
            
            <!-- 图表标签切换 -->
            <div class="tab-buttons mb-4">
                <div class="tab-button active">日统计</div>
                <div class="tab-button">周统计</div>
                <div class="tab-button">月统计</div>
            </div>
            
            <!-- 工单趋势图 -->
            <div class="card p-4 mb-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-base font-medium">工单趋势</h3>
                    <div class="text-xs text-gray-500">单位: 件</div>
                </div>
                <div id="trend-chart" style="width: 100%; height: 200px;"></div>
            </div>
            
            <!-- 工单类型分布 -->
            <div class="card p-4 mb-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-base font-medium">工单类型分布</h3>
                    <div class="text-xs text-gray-500">单位: %</div>
                </div>
                <div id="type-chart" style="width: 100%; height: 200px;"></div>
            </div>
            
            <!-- 处理效率 -->
            <div class="card p-4 mb-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-base font-medium">处理效率</h3>
                    <div class="text-xs text-gray-500">平均处理时间</div>
                </div>
                <div id="efficiency-chart" style="width: 100%; height: 200px;"></div>
            </div>
            
            <!-- 满意度分析 -->
            <div class="card p-4 mb-4">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-base font-medium">满意度分析</h3>
                    <div class="text-xs text-gray-500">用户评价</div>
                </div>
                <div class="flex justify-center items-center">
                    <div id="satisfaction-chart" style="width: 100%; height: 200px;"></div>
                </div>
            </div>
        </div>
        
        <!-- 工程师导航栏 -->
        <div id="engineerTabBar" class="tab-bar">
            <a href="home.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="work-order.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline></svg>
                <span class="text-xs mt-1">工单</span>
            </a>
            <a href="knowledge.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path><path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path></svg>
                <span class="text-xs mt-1">知识库</span>
            </a>
            <a href="dashboard.html" class="tab-item active">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="7" height="7"></rect><rect x="14" y="3" width="7" height="7"></rect><rect x="14" y="14" width="7" height="7"></rect><rect x="3" y="14" width="7" height="7"></rect></svg>
                <span class="text-xs mt-1">看板</span>
            </a>
            <a href="profile.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
    
    <script>
        // 检查用户角色，确保只有工程师可以访问
        function checkUserRole() {
            const urlParams = new URLSearchParams(window.location.search);
            const role = urlParams.get('role');
            
            if (role !== 'engineer') {
                // 如果不是工程师角色，重定向到首页
                window.location.href = 'home.html';
            }
        }
        
        // 页面加载时检查角色
        window.addEventListener('DOMContentLoaded', checkUserRole);
        
        // 初始化图表
        // 1. 工单趋势图
        const trendChart = echarts.init(document.getElementById('trend-chart'));
        const trendOption = {
            color: ['#409EFF', '#67C23A'],
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['新建工单', '完成工单'],
                right: 0,
                top: 0,
                textStyle: {
                    color: '#666',
                    fontSize: 12
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '25%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['4/1', '4/2', '4/3', '4/4', '4/5', '4/6', '4/7'],
                axisLine: {
                    lineStyle: {
                        color: '#ddd'
                    }
                },
                axisLabel: {
                    color: '#666',
                    fontSize: 10
                }
            },
            yAxis: {
                type: 'value',
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    color: '#666',
                    fontSize: 10
                },
                splitLine: {
                    lineStyle: {
                        color: '#eee'
                    }
                }
            },
            series: [
                {
                    name: '新建工单',
                    type: 'bar',
                    barWidth: '40%',
                    stack: 'total',
                    itemStyle: {
                        borderRadius: [4, 4, 0, 0]
                    },
                    data: [12, 19, 15, 22, 18, 8, 5]
                },
                {
                    name: '完成工单',
                    type: 'bar',
                    barWidth: '40%',
                    stack: 'total',
                    itemStyle: {
                        borderRadius: [4, 4, 0, 0]
                    },
                    data: [10, 15, 18, 20, 15, 10, 6]
                }
            ]
        };
        trendChart.setOption(trendOption);
        
        // 2. 工单类型分布
        const typeChart = echarts.init(document.getElementById('type-chart'));
        const typeOption = {
            color: ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399'],
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'horizontal',
                bottom: 0,
                data: ['网络问题', '软件安装', '硬件故障', '账户权限', '其他'],
                textStyle: {
                    color: '#666',
                    fontSize: 12
                }
            },
            series: [
                {
                    name: '工单类型',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 5,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '14',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: [
                        { value: 35, name: '网络问题' },
                        { value: 25, name: '软件安装' },
                        { value: 20, name: '硬件故障' },
                        { value: 15, name: '账户权限' },
                        { value: 5, name: '其他' }
                    ]
                }
            ]
        };
        typeChart.setOption(typeOption);
        
        // 3. 处理效率
        const efficiencyChart = echarts.init(document.getElementById('efficiency-chart'));
        const efficiencyOption = {
            color: ['#409EFF'],
            tooltip: {
                trigger: 'axis',
                formatter: '{b}: {c} 小时'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                top: '5%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['网络问题', '软件安装', '硬件故障', '账户权限', '其他'],
                axisLine: {
                    lineStyle: {
                        color: '#ddd'
                    }
                },
                axisLabel: {
                    color: '#666',
                    fontSize: 10,
                    interval: 0,
                    rotate: 30
                }
            },
            yAxis: {
                type: 'value',
                name: '小时',
                nameTextStyle: {
                    color: '#666',
                    fontSize: 10
                },
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    color: '#666',
                    fontSize: 10
                },
                splitLine: {
                    lineStyle: {
                        color: '#eee'
                    }
                }
            },
            series: [
                {
                    data: [4.5, 2.1, 7.2, 1.5, 3.8],
                    type: 'bar',
                    barWidth: '50%',
                    itemStyle: {
                        borderRadius: [4, 4, 0, 0]
                    }
                }
            ]
        };
        efficiencyChart.setOption(efficiencyOption);
        
        // 4. 满意度分析
        const satisfactionChart = echarts.init(document.getElementById('satisfaction-chart'));
        const satisfactionOption = {
            series: [
                {
                    type: 'gauge',
                    startAngle: 180,
                    endAngle: 0,
                    min: 0,
                    max: 100,
                    splitNumber: 5,
                    radius: '100%',
                    axisLine: {
                        lineStyle: {
                            width: 20,
                            color: [
                                [0.6, '#F56C6C'],
                                [0.8, '#E6A23C'],
                                [1, '#67C23A']
                            ]
                        }
                    },
                    pointer: {
                        icon: 'path://M12,0 L12,9 L16,9 L12,16 L8,9 L12,9 L12,0z',
                        length: '60%',
                        width: 8,
                        offsetCenter: [0, '5%'],
                        itemStyle: {
                            color: '#409EFF'
                        }
                    },
                    axisTick: {
                        length: 12,
                        lineStyle: {
                            color: 'auto',
                            width: 2
                        }
                    },
                    splitLine: {
                        length: 20,
                        lineStyle: {
                            color: 'auto',
                            width: 3
                        }
                    },
                    axisLabel: {
                        color: '#666',
                        fontSize: 10,
                        distance: -60,
                        formatter: function(value) {
                            if (value === 0 || value === 100) {
                                return '';
                            }
                            return value;
                        }
                    },
                    title: {
                        offsetCenter: [0, '-20%'],
                        fontSize: 14,
                        color: '#333'
                    },
                    detail: {
                        fontSize: 30,
                        offsetCenter: [0, '25%'],
                        valueAnimation: true,
                        formatter: function(value) {
                            return value + '%';
                        },
                        color: '#409EFF'
                    },
                    data: [
                        {
                            value: 87,
                            name: '满意度'
                        }
                    ]
                }
            ]
        };
        satisfactionChart.setOption(satisfactionOption);
        
        // 处理窗口大小变化
        window.addEventListener('resize', function() {
            trendChart.resize();
            typeChart.resize();
            efficiencyChart.resize();
            satisfactionChart.resize();
        });
        
        // 标签页切换
        const tabButtons = document.querySelectorAll('.tab-button');
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                tabButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                // 这里可以添加切换数据的逻辑
            });
        });
    </script>
</body>
</html> 