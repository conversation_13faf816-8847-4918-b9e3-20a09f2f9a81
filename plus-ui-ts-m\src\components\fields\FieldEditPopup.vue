<template>
  <van-popup
    v-model:show="show"
    position="bottom"
    :style="{ height: '50%', width: '100%' }"
    round
    safe-area-inset-bottom
    @close="onClose"
  >
    <div class="popup-container">
      <van-nav-bar
        :title="field?.fieldName || '编辑字段'"
        left-text="取消"
        right-text="确认"
        @click-left="onCancel"
        @click-right="onConfirm"
        v-if="!hasBuiltInButtons"
      />
      
      <div class="popup-content">
        <!-- PLAIN_TEXT, MULTILINE_EDIT_BOX -->
        <van-field
          v-if="field && [2, 6].includes(field.fieldType)"
          v-model="localValue"
          type="textarea"
          rows="4"
          autosize
          maxlength="200"
          show-word-limit
        />
        
        <!-- DROPDOWN_LIST, COMBOBOX -->
        <van-picker
          v-else-if="field && [3, 10].includes(field.fieldType)"
          v-model="pickerValue"
          :columns="formattedChoices"
          :columns-field-names="{ text: 'text', value: 'value' }"
          title="请选择"
          @confirm="onPickerConfirm"
          @cancel="onCancel"
        />
        
        <!-- MULTIPLE_SELECTION_LISTBOX -->
        <van-checkbox-group
          v-else-if="field && field.fieldType === 4"
          v-model="localValue"
          direction="horizontal"
        >
          <van-checkbox
            v-for="choice in choices"
            :key="choice.choiceId"
            :name="choice.choiceId"
          >
            {{ choice.choiceName }}
          </van-checkbox>
        </van-checkbox-group>
        
        <!-- CHECKBOX -->
        <van-switch
          v-else-if="field && field.fieldType === 7"
          v-model="localValue"
          size="20px"
        />
        
        <!-- RADIO_BUTTON -->
        <van-radio-group
          v-else-if="field && field.fieldType === 8"
          v-model="localValue"
          direction="horizontal"
        >
          <van-radio
            v-for="choice in choices"
            :key="choice.choiceId"
            :name="choice.choiceId"
          >
            {{ choice.choiceName }}
          </van-radio>
        </van-radio-group>
        
        <!-- AMOUNT -->
        <van-field
          v-else-if="field && field.fieldType === FieldTypeEnum.AMOUNT"
          v-model="localValue"
          type="number"
          placeholder="请输入金额"
          input-align="right"
        />
        
        <!-- 日期时间字段 (DATE_TIME_FIELD) -->
        <van-date-picker
          v-else-if="field && field.fieldType === FieldTypeEnum.DATE_TIME_FIELD"
          :model-value="dateValue"
          :min-date="minDate"
          :max-date="maxDate"
          @confirm="onDateConfirm"
          @cancel="onCancel"
        />
        
        <!-- 其他类型 -->
        <van-field
          v-else
          v-model="localValue"
          placeholder="请输入内容"
        />
      </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { PageFieldVo } from '@/api/field/types'
import { FieldTypeEnum } from '@/enums/STCommonEnum'

interface Props {
  modelValue: any
  field: PageFieldVo | null
  choices?: Array<{ choiceId: number | string; choiceName: string }> | null
  show: boolean
}

interface Emits {
  (e: 'update:modelValue', value: any): void
  (e: 'update:show', value: boolean): void
  (e: 'confirm', value: any): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 格式化日期为数组格式 [YYYY, MM, DD]
const formatDateToArray = (date: Date): string[] => {
  const year = date.getFullYear().toString()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  return [year, month, day]
}

// 从数组格式解析回日期
const parseArrayToDate = (dateArray: string[]): Date => {
  if (!Array.isArray(dateArray) || dateArray.length !== 3) {
    return new Date()
  }
  const [year, month, day] = dateArray
  return new Date(parseInt(year), parseInt(month) - 1, parseInt(day))
}

const localValue = ref<any>(props.modelValue)
const dateValue = ref(formatDateToArray(new Date()))
const pickerValue = ref<number | string>('')

// 日期选择器的日期范围
const minDate = new Date(1900, 0, 1)
const maxDate = new Date(2100, 11, 31)

// 监听props变化更新localValue
watch(() => props.modelValue, (newValue) => {
  localValue.value = newValue
  
  // 确保日期类型字段的值正确格式化
  if (props.field && [FieldTypeEnum.DATE_TIME_FIELD].includes(props.field.fieldType)) {
    if (newValue && typeof newValue === 'string') {
      const date = new Date(newValue)
      dateValue.value = formatDateToArray(date)
    } else if (newValue instanceof Date) {
      dateValue.value = formatDateToArray(newValue)
    } else {
      dateValue.value = formatDateToArray(new Date())
    }
  }
  
  // 初始化picker值 - 确保是字符串或数字类型
  if (props.field && [3, 10].includes(props.field.fieldType)) {
    if (typeof newValue === 'string' || typeof newValue === 'number') {
      pickerValue.value = newValue
    } else if (newValue && typeof newValue === 'object') {
      // 如果是对象，尝试获取value
      pickerValue.value = newValue.choiceId || newValue.id || newValue.value || ''
    } else {
      pickerValue.value = ''
    }
  }
  
  // 初始化日期值
  if (props.field && props.field.fieldType === FieldTypeEnum.DATE_TIME_FIELD) {
    if (newValue && typeof newValue === 'string') {
      const date = new Date(newValue)
      dateValue.value = formatDateToArray(date)
    } else if (newValue instanceof Date) {
      dateValue.value = formatDateToArray(newValue)
    } else {
      dateValue.value = formatDateToArray(new Date())
    }
  }
}, { immediate: true })

const show = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 格式化选择项 - 确保是数组格式
const formattedChoices = computed(() => {
  if (!props.choices || !Array.isArray(props.choices)) {
    console.log('Invalid choices format:', props.choices);
    return []
  }
  
  return props.choices.map(choice => {
    if (typeof choice === 'object' && choice.choiceName && choice.choiceId !== undefined) {
      return {
        text: choice.choiceName,
        value: choice.choiceId
      };
    }
    return {
      text: String(choice),
      value: String(choice)
    };
  });
})

// 判断字段类型是否有内置的确认/取消按钮
const hasBuiltInButtons = computed(() => {
  return props.field && [FieldTypeEnum.DATE_TIME_FIELD, 3, 10].includes(props.field.fieldType)
})

const onConfirm = () => {
  let finalValue = localValue.value
  
  // 处理日期类型字段的格式化
  if (props.field && [FieldTypeEnum.DATE_TIME_FIELD].includes(props.field.fieldType)) {
    if (finalValue instanceof Date) {
      finalValue = finalValue.toISOString()
    } else if (Array.isArray(finalValue)) {
      // 处理从日期选择器返回的数组格式
      const date = parseArrayToDate(finalValue)
      finalValue = date.toISOString()
    }
  }
  
  emit('confirm', finalValue)
  show.value = false
}

const onCancel = () => {
  emit('cancel')
  show.value = false
}

const onClose = () => {
  emit('cancel')
}

const onPickerConfirm = ({ selectedOptions }: { selectedOptions: any[] }) => {
  // 处理van-picker的返回值 - 从selectedOptions中获取value
  if (selectedOptions && selectedOptions.length > 0) {
    const selectedValue = selectedOptions[0].value
    localValue.value = selectedValue
    pickerValue.value = selectedValue
  }
}

const onDateConfirm = (value: string[]) => {
  const selectedDate = parseArrayToDate(value)
  localValue.value = selectedDate
  dateValue.value = value
}
</script>

<style scoped>
.popup-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.van-checkbox-group,
.van-radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.van-checkbox,
.van-radio {
  margin-right: 10px;
}
</style>