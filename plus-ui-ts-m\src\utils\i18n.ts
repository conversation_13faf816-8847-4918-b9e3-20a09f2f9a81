// translate router.meta.title, be used in breadcrumb sidebar tagsview
import i18n from '@/lang/index';

/**
 * 获取国际化路由，如果不存在则原生返回
 * @param title 路由名称
 * @returns {string}
 */
export const translateRouteTitle = (title: string): string => {
  // 修复类型推断过深的问题
  const key = 'route.' + title;
  const hasKey: boolean = (i18n.global.te as any)(key);
  if (hasKey) {
    const translatedTitle: string = (i18n.global.t as any)(key);
    return translatedTitle;
  }
  return title;
};