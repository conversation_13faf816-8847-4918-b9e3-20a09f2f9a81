/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AmountFieldPopup: typeof import('./components/fields/AmountFieldPopup.vue')['default']
    AnnouncementList: typeof import('./components/AnnouncementList.vue')['default']
    CustomPage: typeof import('./components/CustomPage.vue')['default']
    DropdownField: typeof import('./components/fields/DropdownField.vue')['default']
    DropdownFieldPopup: typeof import('./components/fields/DropdownFieldPopup.vue')['default']
    FieldEditPopup: typeof import('./components/fields/FieldEditPopup.vue')['default']
    FieldPopupWrapper: typeof import('./components/fields/FieldPopupWrapper.vue')['default']
    FieldSelectorPopup: typeof import('./components/FieldSelectorPopup.vue')['default']
    FileField: typeof import('./components/fields/FileField.vue')['default']
    ImageField: typeof import('./components/fields/ImageField.vue')['default']
    ItemHistory: typeof import('./components/ItemHistory.vue')['default']
    ItemList: typeof import('./components/ItemList.vue')['default']
    KnowledgeList: typeof import('./components/KnowledgeList.vue')['default']
    LocationField: typeof import('./components/fields/LocationField.vue')['default']
    MultipleSelectionField: typeof import('./components/fields/MultipleSelectionField.vue')['default']
    MultipleSelectionFieldPopup: typeof import('./components/fields/MultipleSelectionFieldPopup.vue')['default']
    PlainTextField: typeof import('./components/fields/PlainTextField.vue')['default']
    PlainTextFieldPopup: typeof import('./components/fields/PlainTextFieldPopup.vue')['default']
    ProjectList: typeof import('./components/ProjectList.vue')['default']
    ReportContent: typeof import('./components/ReportContent.vue')['default']
    RichTextField: typeof import('./components/fields/RichTextField.vue')['default']
    RichTextFieldPopup: typeof import('./components/fields/RichTextFieldPopup.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ServiceList: typeof import('./components/ServiceList.vue')['default']
    SignatureField: typeof import('./components/fields/SignatureField.vue')['default']
    UserField: typeof import('./components/fields/UserField.vue')['default']
    VanButton: typeof import('vant/es')['Button']
    VanCell: typeof import('vant/es')['Cell']
    VanCellGroup: typeof import('vant/es')['CellGroup']
    VanCheckbox: typeof import('vant/es')['Checkbox']
    VanCheckboxGroup: typeof import('vant/es')['CheckboxGroup']
    VanCollapse: typeof import('vant/es')['Collapse']
    VanCollapseItem: typeof import('vant/es')['CollapseItem']
    VanDatePicker: typeof import('vant/es')['DatePicker']
    VanDatetimePicker: typeof import('vant/es')['DatetimePicker']
    VanField: typeof import('vant/es')['Field']
    VanForm: typeof import('vant/es')['Form']
    VanIcon: typeof import('vant/es')['Icon']
    VanImage: typeof import('vant/es')['Image']
    VanList: typeof import('vant/es')['List']
    VanLoading: typeof import('vant/es')['Loading']
    VanNavBar: typeof import('vant/es')['NavBar']
    VanNumberKeyboard: typeof import('vant/es')['NumberKeyboard']
    VanPicker: typeof import('vant/es')['Picker']
    VanPopup: typeof import('vant/es')['Popup']
    VanPullRefresh: typeof import('vant/es')['PullRefresh']
    VanRadio: typeof import('vant/es')['Radio']
    VanRadioGroup: typeof import('vant/es')['RadioGroup']
    VanSearch: typeof import('vant/es')['Search']
    VanSwitch: typeof import('vant/es')['Switch']
    VanTabbar: typeof import('vant/es')['Tabbar']
    VanTabbarItem: typeof import('vant/es')['TabbarItem']
    VanTag: typeof import('vant/es')['Tag']
  }
}
