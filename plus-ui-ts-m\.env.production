# 生产环境配置
# NODE_ENV=production

# 应用标题
VITE_APP_TITLE=ITSM移动端

# API基础URL - 生产环境留空，使用相对路径，由Web服务器代理
VITE_API_BASE_URL=

# 生产环境
VITE_APP_BASE_API=/prod-api
# 应用基础路径
VITE_BASE_URL=/mobile/

# 应用端口
VITE_APP_PORT=80

# 是否启用调试模式
VITE_DEBUG=false

# 是否启用性能监控
VITE_ENABLE_PERFORMANCE_MONITORING=true

# 是否启用错误上报
VITE_ENABLE_ERROR_REPORTING=true

# 是否启用用户行为追踪
VITE_ENABLE_USER_TRACKING=false

# 构建输出目录
VITE_BUILD_OUTPUT_DIR=dist

# 是否生成sourcemap
VITE_GENERATE_SOURCEMAP=false

# CDN基础URL
VITE_CDN_BASE_URL=

# 是否启用gzip压缩
VITE_ENABLE_GZIP=true

# 是否启用brotli压缩
VITE_ENABLE_BROTLI=true

# 是否启用图片压缩
VITE_ENABLE_IMAGE_COMPRESSION=true

# 是否启用CSS代码分割
VITE_ENABLE_CSS_CODE_SPLITTING=true

# 是否启用JS代码分割
VITE_ENABLE_JS_CODE_SPLITTING=true

# 是否启用tree shaking
VITE_ENABLE_TREE_SHAKING=true

# 是否启用minification
VITE_ENABLE_MINIFICATION=true

# 是否启用chunk大小警告
VITE_ENABLE_CHUNK_SIZE_WARNING=true

# 最大chunk大小（KB）
VITE_MAX_CHUNK_SIZE=500

# 是否启用预加载
VITE_ENABLE_PRELOAD=true

# 是否启用预取
VITE_ENABLE_PREFETCH=true

# 接口加密功能开关
VITE_APP_ENCRYPT=true

# RSA公钥
VITE_APP_RSA_PUBLIC_KEY=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==

# RSA私钥
VITE_APP_RSA_PRIVATE_KEY=MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAmc3CuPiGL/LcIIm7zryCEIbl1SPzBkr75E2VMtxegyZ1lYRD+7TZGAPkvIsBcaMs6Nsy0L78n2qh+lIZMpLH8wIDAQABAkEAk82Mhz0tlv6IVCyIcw/s3f0E+WLmtPFyR9/WtV3Y5aaejUkU60JpX4m5xNR2VaqOLTZAYjW8Wy0aXr3zYIhhQQIhAMfqR9oFdYw1J9SsNc+CrhugAvKTi0+BF6VoL6psWhvbAiEAxPPNTmrkmrXwdm/pQQu3UOQmc2vCZ5tiKpW10CgJi8kCIFGkL6utxw93Ncj4exE/gPLvKcT+1Emnoox+O9kRXss5AiAMtYLJDaLEzPrAWcZeeSgSIzbL+ecokmFKSDDcRske6QIgSMkHedwND1olF8vlKsJUGK3BcdtM8w4Xq7BpSBwsloE=

# 客户端id
VITE_APP_CLIENT_ID=e5cd7e4891bf95d1d19206ce24a7b32e

# websocket开关
VITE_APP_WEBSOCKET=false

# sse开关
VITE_APP_SSE=true