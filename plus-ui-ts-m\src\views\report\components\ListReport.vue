<template>
  <div class="page-container">
    <van-nav-bar title="列表" fixed left-arrow @click-left="$router.back()"/>
    <div class="report-content">
      <van-list>
        <van-cell 
          v-for="item in listData" 
          :key="item.id"
          :title="item.name"
          :label="item.description"
          is-link
          @click="viewDetail(item.id)"
        />
      </van-list>
    </div>
    <!-- 底部导航栏 -->
    <van-tabbar route fixed>
      <van-tabbar-item to="/home" icon="home-o">首页</van-tabbar-item>
      <van-tabbar-item to="/projects" icon="apps-o">项目</van-tabbar-item>
      <van-tabbar-item to="/reports" icon="chart-trending-o">报表</van-tabbar-item>
      <van-tabbar-item to="/profile" icon="user-o">我的</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 示例数据
const listData = ref([
  { id: 1, name: '服务请求 #SR-001', description: '用户无法登录系统' },
  { id: 2, name: '故障报告 #IR-002', description: '服务器响应缓慢' },
  { id: 3, name: '变更请求 #CR-003', description: '数据库版本升级' },
  { id: 4, name: '问题记录 #PR-004', description: '重复出现的登录问题' },
  { id: 5, name: '知识文档 #KD-005', description: '新员工入职指南' }
])

const exportData = () => {
  console.log('导出数据')
  // 这里可以实现导出功能
}

const viewDetail = (id: number) => {
  console.log('查看详情:', id)
  // 这里可以跳转到详情页面
}

onMounted(() => {
  console.log('列表报表页面已加载')
})
</script>

<style scoped>
.page-container {
    position: relative;
    min-height: 100vh;
    padding-top: 46px;
    padding-bottom: 50px;
    box-sizing: border-box;
  }

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px 0;
  border-bottom: 1px solid #e4e7ed;
}

.report-content {
  background-color: white;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

</style>