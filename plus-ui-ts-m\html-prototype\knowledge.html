<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识库 - ITSM系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="public/static/font/iconfont.css">
    <style>
        @font-face {
            font-family: 'AlibabaPuHuiTi';
            src: url('public/static/font/AlibabaPuHuiTi-3-45-Light.ttf') format('ttf');
            font-weight: 300;
            font-style: normal;
        }
        @font-face {
            font-family: 'AlibabaPuHuiTi';
            src: url('public/static/font/AlibabaPuHuiTi-3-55-Regular.ttf') format('ttf');
            font-weight: 400;
            font-style: normal;
        }
        @font-face {
            font-family: 'AlibabaPuHuiTi';
            src: url('public/static/font/AlibabaPuHuiTi-3-65-Medium.ttf') format('ttf');
            font-weight: 500;
            font-style: normal;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'AlibabaPuHuiTi', -apple-system, BlinkMacSystemFont, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background-color: #f5f7fa;
            width: 100%;
            max-width: 430px;
            margin: 0 auto;
            height: 100vh;
            overflow: hidden;
            position: relative;
            border-radius: 40px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        
        .app-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            background-color: white;
            border-radius: 40px;
        }
        
        .status-bar {
            height: 44px;
            width: 100%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            position: relative;
            z-index: 10;
            border-top-left-radius: 40px;
            border-top-right-radius: 40px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .tab-bar {
            height: 84px;
            width: 100%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-around;
            border-top: 1px solid rgba(0,0,0,0.05);
            padding-bottom: 20px;
            border-bottom-left-radius: 40px;
            border-bottom-right-radius: 40px;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 50px;
            color: #999;
        }
        
        .tab-item.active {
            color: #409EFF;
        }
        
        .content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 15px;
            background-color: #f5f7fa;
        }
        
        .knowledge-card {
            background-color: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        
        .knowledge-item {
            padding: 12px 0;
            border-bottom: 1px solid #f2f2f2;
        }
        
        .knowledge-item:last-child {
            border-bottom: none;
        }
        
        .badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .badge-published {
            background-color: rgba(103, 194, 58, 0.1);
            color: #67C23A;
        }
        
        .badge-unpublished {
            background-color: rgba(230, 162, 60, 0.1);
            color: #E6A23C;
        }
        
        .category-icon {
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f0f8ff;
            border-radius: 8px;
            margin-right: 12px;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #909399;
            margin-bottom: 16px;
        }
        
        .breadcrumb-separator {
            margin: 0 6px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="status-bar">
            <div class="flex items-center">
                <span class="text-black text-sm font-medium">1:21 AM</span>
            </div>
            <div class="flex items-center space-x-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 10a6 6 0 0 0-12 0v8h12v-8z"/><path d="M20 10a8 8 0 0 0-16 0v8h2v-8a6 6 0 0 1 12 0v8h2v-8z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6.33 20.855A10.968 10.968 0 0 1 2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10c-1.97 0-3.823-.518-5.425-1.428"/><path d="M16 12V7"/><path d="M8 12v5"/><path d="M12 16v3"/><path d="M12 7v5"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12a10 10 0 0 1 10 -10v0a10 10 0 0 1 10 10v0a10 10 0 0 1 -10 10h-2a8 8 0 0 0 -8 -8"/><path d="M5 18v-2"/><path d="M2 6v-2h2"/><path d="M20 6v-2h-2"/><path d="M3 10h2"/><path d="M17 14h4"/></svg>
            </div>
        </div>
        
        <div class="flex items-center justify-between bg-white px-4 py-3">
            <h1 class="text-xl font-bold">知识库</h1>
            <div class="flex items-center">
                <a href="#" class="ml-4">
                    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="16"></line><line x1="8" y1="12" x2="16" y2="12"></line></svg>
                </a>
            </div>
        </div>
        
        <div class="content">
            <!-- 搜索框 -->
            <div class="relative mb-4">
                <input type="text" placeholder="搜索知识..." class="w-full bg-white rounded-lg border border-gray-200 pl-10 pr-4 py-2 text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="absolute left-3 top-2.5 text-gray-400"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>
            </div>
            
            <!-- 面包屑 -->
            <div class="breadcrumb">
                <span>启瑞知识库</span>
                <span class="breadcrumb-separator">/</span>
                <span>IT用户知识库</span>
            </div>
            
            <!-- 分类 -->
            <div class="bg-white rounded-lg p-4 mb-4">
                <div class="flex items-center justify-between mb-2">
                    <div class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="orange" viewBox="0 0 24 24"><path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path></svg>
                        <span class="ml-2 font-medium">用户手册</span>
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>
                </div>
                
                <!-- 过滤图标 -->
                <div class="flex justify-end mb-3">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon></svg>
                </div>
            </div>
            
            <!-- 知识列表 -->
            <div class="knowledge-card">
                <div class="knowledge-item">
                    <div class="flex mb-1">
                        <div class="category-icon text-green-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between">
                                <span class="font-medium">1647</span>
                                <span class="badge badge-published">已发布</span>
                            </div>
                            <h3 class="font-medium mb-1">快速账户密码申请</h3>
                            <div class="flex justify-between text-sm text-gray-500">
                                <span>张三</span>
                                <span>更新: 2020/5/16</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="knowledge-item">
                    <div class="flex mb-1">
                        <div class="category-icon text-red-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between">
                                <span class="font-medium">1647</span>
                                <span class="badge badge-unpublished">未发布</span>
                            </div>
                            <h3 class="font-medium mb-1">[网页] 直播间参与者能够加入交互式会议室</h3>
                            <div class="flex justify-between text-sm text-gray-500">
                                <span>李四</span>
                                <span>更新: 2020/5/16</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="knowledge-item">
                    <div class="flex mb-1">
                        <div class="category-icon text-green-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between">
                                <span class="font-medium">1647</span>
                                <span class="badge badge-published">已发布</span>
                            </div>
                            <h3 class="font-medium mb-1">快速账户密码申请</h3>
                            <div class="flex justify-between text-sm text-gray-500">
                                <span>王五</span>
                                <span>更新: 2020/5/16</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="knowledge-item">
                    <div class="flex mb-1">
                        <div class="category-icon text-green-500">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points="14 2 14 8 20 8"></polyline><line x1="16" y1="13" x2="8" y2="13"></line><line x1="16" y1="17" x2="8" y2="17"></line><polyline points="10 9 9 9 8 9"></polyline></svg>
                        </div>
                        <div class="flex-1">
                            <div class="flex justify-between">
                                <span class="font-medium">1647</span>
                                <span class="badge badge-published">已发布</span>
                            </div>
                            <h3 class="font-medium mb-1">快速账户密码申请</h3>
                            <div class="flex justify-between text-sm text-gray-500">
                                <span>赵六</span>
                                <span>更新: 2020/5/16</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 工程师导航栏 -->
        <div id="engineerTabBar" class="tab-bar" style="display: none;">
            <a href="home.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="work-order.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline></svg>
                <span class="text-xs mt-1">工单</span>
            </a>
            <a href="knowledge.html" class="tab-item active">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path><path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path></svg>
                <span class="text-xs mt-1">知识库</span>
            </a>
            <a href="dashboard.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="7" height="7"></rect><rect x="14" y="3" width="7" height="7"></rect><rect x="14" y="14" width="7" height="7"></rect><rect x="3" y="14" width="7" height="7"></rect></svg>
                <span class="text-xs mt-1">看板</span>
            </a>
            <a href="profile.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
        
        <!-- 普通用户导航栏 -->
        <div id="userTabBar" class="tab-bar">
            <a href="home.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="service-catalog.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line></svg>
                <span class="text-xs mt-1">服务目录</span>
            </a>
            <a href="work-order.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline></svg>
                <span class="text-xs mt-1">工单</span>
            </a>
            <a href="knowledge.html" class="tab-item active">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path><path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path></svg>
                <span class="text-xs mt-1">知识库</span>
            </a>
            <a href="profile.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
    
    <script>
        // 检查用户角色并显示对应的导航栏
        function checkUserRole() {
            const urlParams = new URLSearchParams(window.location.search);
            const role = urlParams.get('role');
            
            const engineerTabBar = document.getElementById('engineerTabBar');
            const userTabBar = document.getElementById('userTabBar');
            
            if (role === 'engineer') {
                // 如果是工程师角色
                engineerTabBar.style.display = 'flex';
                userTabBar.style.display = 'none';
            } else {
                // 默认为普通用户角色
                engineerTabBar.style.display = 'none';
                userTabBar.style.display = 'flex';
            }
        }
        
        // 页面加载时检查角色
        window.addEventListener('DOMContentLoaded', checkUserRole);
    </script>
</body>
</html> 