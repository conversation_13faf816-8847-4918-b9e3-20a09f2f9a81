// 引入 Toast 样式覆盖
@use './toast-override.scss';

// 引入字体
@font-face {
  font-family: 'AlibabaPuHuiTi';
  src: url('/static/font/AlibabaPuHuiTi-3-55-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'AlibabaPuHuiTi';
  src: url('/static/font/AlibabaPuHuiTi-3-65-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'AlibabaPuHuiTi';
  src: url('/static/font/AlibabaPuHuiTi-3-45-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
}

// 全局样式变量
:root {
  --primary-color: #1989fa;
  --success-color: #07c160;
  --warning-color: #ff976a;
  --danger-color: #ee0a24;
  --text-color: #323233;
  --text-color-2: #646566;
  --text-color-3: #969799;
  --background-color: #f7f8fa;
  --border-color: #ebedf0;
  --active-color: #f2f3f5;
}

// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'AlibabaPuHuiTi', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-color);
  color: var(--text-color);
  font-size: 14px;
  line-height: 1.5;
}

// 移动端适配
html {
  font-size: calc(100vw / 375 * 14);
}

// 滚动条样式
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

// 通用工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.overflow-hidden {
  overflow: hidden;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

// 页面容器
.page-container {
  min-height: 100vh;
  background-color: var(--background-color);
  padding-bottom: 50px; // 为底部导航留出空间
}

// 内容区域
.content {
  padding: 16px;
}

// 卡片样式
.card {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

// 分割线
.divider {
  height: 1px;
  background-color: var(--border-color);
  margin: 12px 0;
}

// 空状态
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-color-3);
}

.empty-state-icon {
  font-size: 48px;
  margin-bottom: 12px;
  color: var(--text-color-3);
}

// 加载状态
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

// 响应式断点
@media (max-width: 320px) {
  html {
    font-size: calc(100vw / 320 * 14);
  }
}

@media (min-width: 768px) {
  html {
    font-size: 14px;
  }
}