import request from '@/utils/request';
import type { AxiosResponse } from 'axios';
import type { ProjectListData, NoticeVO, UserInfoVO } from './types';

// pc端固定客户端授权id
const clientId = import.meta.env.VITE_APP_CLIENT_ID;

/**
 * @param data {LoginData}
 * @returns
 */

export const getProjectListByUser = (): Promise<AxiosResponse<ProjectListData>>  => {
  return request({
    url: `/servicetrack/project/listByUser`,
    method: 'get'
  });
};

export const listNotice = (): Promise<AxiosResponse<NoticeVO[]>>  => {
  return request({
    url: `/system/notice/list`,
    method: 'get'
  });
};

export const getUserProfile = (): Promise<AxiosResponse<UserInfoVO>> => {
  return request({
    url: '/system/user/profile',
    method: 'get'
  });
};