// 强制覆盖 Vant Toast 样式
.van-toast {
  &.van-toast--fail,
  &.custom-toast {
    background: rgba(0, 0, 0, 0.8) !important;
    border-radius: 12px !important;
    padding: 16px 20px !important;
    min-width: 280px !important;
    
    .van-toast__text {
      color: white !important;
      font-size: 14px !important;
      line-height: 1.4 !important;
      text-align: center !important;
    }
    
    // 强制隐藏所有图标
    .van-toast__icon,
    .van-icon,
    [class*="van-icon"],
    i,
    svg {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
      width: 0 !important;
      height: 0 !important;
      margin: 0 !important;
      padding: 0 !important;
    }
  }
}

