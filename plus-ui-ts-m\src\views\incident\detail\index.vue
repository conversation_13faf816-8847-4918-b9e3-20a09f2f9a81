<template>
    <div class="page-container" :class="{ 'show-scrollbar': showScrollbar }">
      <!-- 顶部栏 -->
      <van-nav-bar title="工单详情" left-arrow @click-left="$router.back()" fixed />
  
      <!-- 工单基本信息 -->
      <div class="detail-header">
        <div class="card-header">
          <div class="title-section">
            <!-- 第一行：工单号 -->
            <div class="work-number">工单号: {{ workOrderData.number }}</div>
            
            <!-- 第二行：标题 -->
            <div class="title-row">
              <h2 class="work-title">{{ workOrderData.title }}</h2>
            </div>
            
            <!-- 第三行：左边显示状态，右边显示负责人 -->
            <div class="status-owner-row">
              <div class="status-container">
                <van-tag 
                  class="status-tag"
                  :style="{
                    backgroundColor: getStatusBackgroundColor(workOrderData.status),
                    color: getStatusColor(workOrderData.status),
                  }"
                  @click="showStatusPopup = true"
                >
                  {{ workOrderData.statusText }}
                </van-tag>
              </div>
              <div class="owner-container">
                <div class="owner-avatar" @click="showOwnerPopup = true">
                  <img :src="workOrderData.owner.avatar" :alt="workOrderData.owner.name" />
                  <span class="owner-name">{{ workOrderData.owner.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
        
       
        <!-- 字段列表 -->
        <CustomPage 
          :project-id="Number(projectId)" 
          :type-id="Number(typeId)" 
          :item-id="Number(itemId)" 
        />
  
  
      <!-- 处理进度折叠面板 -->
      <van-collapse v-model="activeNames" class="detail-card">
        <van-collapse-item title="处理进度" name="progress">
          <ItemHistory :project-id="Number(projectId)" :item-id="Number(itemId)" />
        </van-collapse-item>
      </van-collapse>
  
      <!-- 底部导航栏 -->
      <van-tabbar route fixed>
        <van-tabbar-item to="/home" icon="home-o">首页</van-tabbar-item>
        <van-tabbar-item to="/projects" icon="apps-o">项目</van-tabbar-item>
        <van-tabbar-item to="/reports" icon="chart-trending-o">报表</van-tabbar-item>
        <van-tabbar-item to="/profile" icon="user-o">我的</van-tabbar-item>
      </van-tabbar>
    </div>
    

    

  
    <!-- Owner选择弹窗 -->
    <van-popup 
      v-model:show="showOwnerPopup" 
      position="bottom" 
      :style="{ width: '100%', height: 'auto' }" 
      safe-area-inset-bottom
      round>
      <div class="popup-container">
        <van-nav-bar
          title="选择负责人"
          left-text="取消"
          right-text="确认"
          @click-left="showOwnerPopup = false"
          @click-right="saveOwnerValue"
        />
        <div class="popup-content">
          <van-radio-group v-model="selectedOwnerValue" class="radio-group">
            <van-cell-group>
              <van-cell 
                v-for="(option, index) in ownerOptions" 
                :key="index" 
                clickable 
                @click="selectedOwnerValue = option.value">
                <div class="owner-radio-cell">
                  <van-radio :name="option.value" />
                  <img :src="option.avatar" :alt="option.label" class="owner-option-avatar" />
                  <span class="owner-radio-label">{{ option.label }}</span>
                </div>
              </van-cell>
            </van-cell-group>
          </van-radio-group>
        </div>
      </div>
    </van-popup>
  
    <!-- Status选择弹窗 -->
    <van-popup 
      v-model:show="showStatusPopup" 
      position="bottom" 
      :style="{ width: '100%', height: 'auto' }" 
      safe-area-inset-bottom
      round>
      <div class="popup-container">
        <van-nav-bar
          title="选择流程状态"
          left-text="取消"
          right-text="确认"
          @click-left="showStatusPopup = false"
          @click-right="saveStatusValue"
        />
        <div class="popup-content">
          <van-radio-group v-model="selectedStatusValue" class="radio-group">
            <van-cell-group>
              <van-cell 
                v-for="(option, index) in statusOptions" 
                :key="index" 
                clickable 
                @click="selectedStatusValue = option.value">
                <div class="status-radio-cell">
                  <van-radio :name="option.value" />
                  <span class="status-badge-popup" :class="`status-${option.status}`">{{ option.statusText }}</span>
                  <span class="status-radio-label">——————> {{ option.label }}</span>
                  
                </div>
              </van-cell>
            </van-cell-group>
          </van-radio-group>
        </div>
      </div>
    </van-popup>
  </template>
  
  <script setup lang="ts">
  import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { FieldTypeEnum } from '@/enums/STCommonEnum'
import ItemHistory from '@/components/ItemHistory.vue'
import CustomPage from '@/components/CustomPage.vue'
import { getItem, getStateList } from '@/api/item/index'
import type { ItemVO, stateVO } from '@/api/item/types'
import { Tag } from 'vant'
import { SystemFieldEnum } from '@/enums/STCommonEnum'

// 注册组件
const VanTag = Tag
  
  // 定义字段类型
  interface FieldValue {
    [key: string]: any
  }
  
  const route = useRoute()
  const itemId = route.query.itemId as string
  const projectId = route.query.projectId as string
  const typeId = route.query.typeId as string
  
  const activeNames = ref(['progress'])
  const showScrollbar = ref(false)
  let scrollTimeout: ReturnType<typeof setTimeout> | null = null
  const stateList = ref<stateVO[]>([])
  
  // Owner弹窗相关数据
  const showOwnerPopup = ref(false)
  const selectedOwnerValue = ref<string>('zhangsan') // 默认选中周佳杰
  // 模拟人员列表数据
  const ownerOptions = ref([
    { 
      label: '周佳杰', 
      value: 'zhangsan',
      avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80'
    },
    { 
      label: '陈媛', 
      value: 'lisi',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80'
    },
    { 
      label: '王并才', 
      value: 'wangwu',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80'
    },
    { 
      label: '卜佳伟', 
      value: 'zhaoliu',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80'
    }
  ])
  
  // Status弹窗相关数据
  const showStatusPopup = ref(false)
  const selectedStatusValue = ref<string>('processing')
  // 模拟流程状态数据
  const statusOptions = ref([
    { 
      label: '待处理', 
      value: 'pending',
      status: 'pending',
      statusText: '待处理'
    },
    { 
      label: '处理中', 
      value: 'processing',
      status: 'processing',
      statusText: '处理中'
    },
    { 
      label: '已完成', 
      value: 'completed',
      status: 'completed',
      statusText: '已完成'
    },
    { 
      label: '已关闭', 
      value: 'closed',
      status: 'closed',
      statusText: '已关闭'
    },
    { 
      label: '已取消', 
      value: 'cancelled',
      status: 'cancelled',
      statusText: '已取消'
    }
  ])
  

  
  // 保存Owner值
  const saveOwnerValue = () => {
    const selectedOwner = ownerOptions.value.find(option => option.value === selectedOwnerValue.value)
    if (selectedOwner) {
      workOrderData.value.owner = {
        name: selectedOwner.label,
        avatar: selectedOwner.avatar
      }
    }
    showOwnerPopup.value = false
  }
  
  // 保存Status值
  const saveStatusValue = () => {
    const selectedStatus = statusOptions.value.find(option => option.value === selectedStatusValue.value)
    if (selectedStatus) {
      workOrderData.value.status = selectedStatus.status
      workOrderData.value.statusText = selectedStatus.statusText
    }
    showStatusPopup.value = false
  }
  

  
  // 滚动处理函数
  const handleScroll = () => {
    showScrollbar.value = true
    
    // 清除之前的定时器
    if (scrollTimeout) {
      clearTimeout(scrollTimeout)
    }
    
    // 1秒后隐藏滚动条
    scrollTimeout = setTimeout(() => {
      showScrollbar.value = false
    }, 1000)
  }
  
  // 触摸滚动处理函数（移动端优化）
  const handleTouchStart = () => {
    showScrollbar.value = true
  }
  
  const handleTouchEnd = () => {
    if (scrollTimeout) {
      clearTimeout(scrollTimeout)
    }
    scrollTimeout = setTimeout(() => {
      showScrollbar.value = false
    }, 1000)
  }
  
  // 组件挂载时添加滚动监听
  onMounted(() => {
    const container = document.querySelector('.page-container')
    if (container) {
      container.addEventListener('scroll', handleScroll)
      container.addEventListener('touchstart', handleTouchStart)
      container.addEventListener('touchend', handleTouchEnd)
    }
    
    // 获取工单基本信息
    fetchWorkOrderBasicInfo()
  })
  
  // 组件卸载时移除滚动监听
  onUnmounted(() => {
    const container = document.querySelector('.page-container')
    if (container) {
      container.removeEventListener('scroll', handleScroll)
      container.removeEventListener('touchstart', handleTouchStart)
      container.removeEventListener('touchend', handleTouchEnd)
    }
    if (scrollTimeout) {
      clearTimeout(scrollTimeout)
    }
  })
  
  // 工单基本信息数据
  const workOrderData = ref({
    title: '',
    number: '',
    status: 'processing',
    statusText: '处理中',
    owner: {
      name: '',
      avatar: ''
    }
  })
  
  // 获取工单基本信息
  const fetchWorkOrderBasicInfo = async () => {
    try {
      // 获取状态列表
      await loadStateList();
      
      const response = await getItem(Number(projectId), Number(itemId))
      const itemData: ItemVO = response.data
      
      // 根据fieldId获取对应的value
      const titleField = itemData.fields.find(field => field.fieldId === 1)
      const statusField = itemData.fields.find(field => field.fieldId === 3)
      const ownerField = itemData.fields.find(field => field.fieldId === 4)
      
      // 更新工单基本信息
      workOrderData.value.title = titleField?.value || ''
      workOrderData.value.number = itemId  // 工单号为当前路由中的itemId
      
      // 状态信息需要进一步处理，这里只是示例
      if (statusField?.value) {
        workOrderData.value.status = statusField.value
        workOrderData.value.statusText = statusField.value
      }
      
      // 负责人信息需要进一步处理，这里只是示例
      if (ownerField?.value) {
        workOrderData.value.owner.name = ownerField.value
        workOrderData.value.owner.avatar = 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80'  // 示例头像
      }
    } catch (error) {
      console.error('获取工单基本信息失败:', error)
    }
  }
  
  // 获取状态颜色
  const getStatusColor = (status: string): string => {
    // 通过状态值查找状态列表中的对应项
    if (stateList.value.length > 0) {
      const state = stateList.value.find(s => s.stateName === status)
      if (state?.stateColor) {
        return state.stateColor
      } else if (state?.stateOptionId === 0) {
        // 当stateOptionId为0时，返回#E6A23C
        return '#E6A23C'
      } else if (state?.stateOptionId === 1) {
        // 当stateOptionId为1时，返回#67C23A
        return '#67C23A'
      }
    }
    
    // 默认颜色
    const statusColorMap: Record<string, string> = {
      'pending': '#909399',
      'processing': '#E6A23C',
      'completed': '#67C23A',
      'closed': '#67C23A',
      'cancelled': '#909399'
    }
    return statusColorMap[status] || '#909399'
  }
  
  // 生成浅色背景色
  const getStatusBackgroundColor = (status: string): string => {
    const color = getStatusColor(status)
    if (!color) return '#f0f0f0'
    
    // 如果是十六进制颜色
    if (color.startsWith('#')) {
      // 将十六进制颜色转换为RGB
      const hex = color.replace('#', '')
      const r = parseInt(hex.substr(0, 2), 16)
      const g = parseInt(hex.substr(2, 2), 16)
      const b = parseInt(hex.substr(4, 2), 16)
      
      // 生成浅色背景（添加白色混合，透明度为0.2）
      return `rgba(${r}, ${g}, ${b}, 0.2)`
    }
    
    // 如果是RGB颜色
    if (color.startsWith('rgb')) {
      const rgb = color.match(/\d+/g)
      if (rgb && rgb.length >= 3) {
        const r = parseInt(rgb[0])
        const g = parseInt(rgb[1])
        const b = parseInt(rgb[2])
        return `rgba(${r}, ${g}, ${b}, 0.2)`
      }
    }
    
    // 默认返回浅灰色
    return '#f0f0f0'
  }
  
  // 获取状态列表
  const loadStateList = async () => {
    try {
      const response = await getStateList(Number(projectId))
      // 根据实际响应结构获取数据
      stateList.value = (response as any).rows || (response as any).data || []
    } catch (error) {
      console.error('加载状态列表失败:', error)
    }
  }
  

  

  
  </script>
  
  <style scoped>
  .page-container {
    padding-top: 46px;
    padding-bottom: 50px;
    background-color: #f5f7fa;
    min-height: 100vh;
    overflow-y: auto;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    -webkit-overflow-scrolling: touch; /* 移动端平滑滚动 */
    position: relative;
    height: 100vh;
    box-sizing: border-box;
  }
  
  /* 弹窗全屏布局样式 */
  .full-height {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .flex-1 {
    flex: 1;
    overflow: hidden;
  }
  
  .full-height-field {
    height: 100%;
  }
  
  .full-height-field :deep(.van-field__control) {
    height: 100%;
  }
  
  .page-container::-webkit-scrollbar {
    width: 8px;
    background-color: transparent;
  }
  
  .page-container::-webkit-scrollbar-track {
    background-color: transparent;
  }
  
  .page-container::-webkit-scrollbar-thumb {
    background-color: transparent;
    border-radius: 4px;
    transition: background-color 0.3s ease;
  }
  
  .page-container::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.5);
  }
  
  .page-container.show-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.4) !important;
  }
  
  /* 确保滚动条在显示时有足够的对比度 */
  .page-container.show-scrollbar::-webkit-scrollbar {
    background-color: rgba(0, 0, 0, 0.05);
  }
  
  /* 移动端优化 */
  @media (max-width: 768px) {
    .page-container {
      padding-bottom: 60px; /* 为底部导航栏留出更多空间 */
    }
    
    .page-container::-webkit-scrollbar {
      width: 6px; /* 移动端滚动条更细 */
    }
    
    .page-container.show-scrollbar::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.4) !important;
    }
  }
  .detail-header {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
    margin: 15px;
    padding: 20px;
    overflow: hidden;
  }
  
  .detail-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
    margin: 15px;
    padding: 5px;
    overflow: hidden;
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 5px;
  }
  
  .title-section {
    flex: 1;
  }
  
  .title-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
  }
  
  .work-title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin: 5px 0;
    flex: 1;
  }
  
  .owner-avatar {
    margin-left: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
  }
  
  .owner-avatar img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
  }
  
  .owner-avatar:hover img {
    transform: scale(1.05);
  }
  
  .owner-radio-cell {
    display: flex;
    align-items: center;
    width: 100%;
  }
  
  .owner-option-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-left: 12px;
    margin-right: 12px;
  }
  
  .owner-radio-label {
    font-size: 14px;
    color: #333;
  }
  
  .status-radio-cell {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    text-align: left;
  }
  
  .status-radio-label {
    font-size: 13px;
    color: #333;
    flex: 1;
    margin-left: 12px;
  }
  
  .status-badge-popup {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    margin-left: 8px;
  }
  
  .status-row {
    margin-top: 4px;
  }
  
  .work-number {
    font-size: 14px;
    color: #999;
  }
  
  .status-tag {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
    display: inline-block;
    text-align: center;
    min-width: 60px;
    cursor: pointer;
    transition: transform 0.2s ease;
  }
  
  .status-tag:hover {
    transform: scale(1.05);
  }
  
  /* 字段样式 */
  .field-label {
    color: #999;
    font-size: 14px;
  }
  
  .field-value {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
  
  .field-value-vertical {
    width: 100%;
  }
  
  .field-value.editable-field {
    justify-content: flex-start; /* 确保编辑状态也左对齐 */
    padding: 0;
    margin: 0;
  }
  
  .field-value-left {
    width: 100%;
    text-align: left;
  }
  
  .normal-value {
    color: #333;
    font-size: 14px;
    text-align: left;
  }
  
  .empty-value {
    color: #ccc;
    font-size: 14px;
    text-align: left;
  }
  
  /* 可编辑字段样式 */
  .editable-field {
    padding: 6px 0;
    font-size: 14px;
    color: #333;
    width: 100%;
  }
  
  .editable-field :deep(.van-field__control) {
    font-size: 14px;
    color: #333;
    line-height: 1.5;
    text-align: left;
    padding: 6px 0;
  }
  
  .editable-field-container {
    width: 100%;
    padding: 6px 0;
    line-height: 1.5;
    box-sizing: border-box;
    text-align: left;
  }
  
  /* 用户信息样式 */
  .user-info {
    display: flex;
    align-items: center;
  }
  
  .avatar-small {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 8px;
  }
  
  /* 优先级样式 */
  .priority-info {
    display: flex;
    align-items: center;
  }
  
  .priority-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
  }
  
  .priority-high {
    background-color: #F56C6C;
  }
  
  .priority-medium {
    background-color: #E6A23C;
  }
  
  .priority-low {
    background-color: #67C23A;
  }
  
  /* 标签样式 */
  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .tag {
    background-color: #f0f2f5;
    color: #666;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
  }
  
  /* 复选框样式 */
  /* .checked {
    color: #F56C6C;
    font-weight: 500;
  } */
  
  .unchecked {
    color: #ccc;
  }
  
  /* 金额样式 */
  .amount-value {
    color: #F56C6C;
    font-weight: 500;
  }
  
  /* 附件样式 */
  .attachment-item {
    display: flex;
    align-items: center;
    background: #f9fafc;
    padding: 8px;
    border-radius: 6px;
    margin-top: 4px;
  }
  
  .attachment-icon {
    color: #409EFF;
    font-size: 16px;
    margin-right: 8px;
  }
  
  .attachment-info {
    flex: 1;
  }
  
  .attachment-name {
    font-size: 12px;
    font-weight: 500;
    color: #333;
  }
  
  .attachment-size {
    font-size: 10px;
    color: #999;
    margin-top: 2px;
  }
  
  .attachment-download {
    color: #999;
    font-size: 14px;
  }
  
  .progress-card {
    margin: 0 15px 15px 15px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  }
  
  .timeline {
    position: relative;
    padding-left: 25px;
  }
  
  .timeline:before {
    content: '';
    position: absolute;
    left: 5px;
    top: 10px;
    bottom: 0;
    width: 2px;
    background-color: #e0e0e0;
  }
  
  .timeline-item {
    position: relative;
    margin-bottom: 20px;
  }
  
  .timeline-item:last-child {
    margin-bottom: 0;
  }
  
  .timeline-item:before {
    content: '';
    position: absolute;
    left: -25px;
    top: 4px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #409EFF;
    border: 2px solid #fff;
    z-index: 1;
  }
  
  .timeline-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
  }
  
  .timeline-time {
    font-size: 12px;
    color: #999;
    margin-bottom: 4px;
  }
  
  .timeline-desc {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
  }
  
  .vertical-cell {
    flex-direction: column;
    align-items: flex-start !important;
  }
  
  .vertical-cell :deep(.van-cell__title) {
    margin-bottom: 8px;
    width: 100%;
    color:#999;
  }
  
  .vertical-cell :deep(.van-cell__value) {
    width: 100%;
    text-align: left;
    margin-left: 0;
  }
  
  .consistent-height {
    min-height: 32px; /* 与 van-field 高度保持一致 */
    box-sizing: border-box;
    display: flex;
    align-items: center;
  }
  
  /* PLAIN_TEXT弹窗样式 */
  .popup-container {
    width: 100vw;
    background-color: #fff;
  }
  
  .popup-content {
    padding: 16px;
  }
  
  .popup-content :deep(.van-field__word-limit) {
    text-align: right;
    margin-top: 4px;
  }
  
  .radio-group {
    width: 100%;
  }
  
  .radio-cell {
    display: flex;
    align-items: center;
    width: 100%;
  }
  
  .radio-label {
    margin-left: 12px;
    font-size: 14px;
    color: #333;
  }
  
  .checkbox-group {
    width: 100%;
  }
  
  .checkbox-cell {
    display: flex;
    align-items: center;
    width: 100%;
  }
  
  .checkbox-label {
    margin-left: 12px;
    font-size: 14px;
    color: #333;
  }
  
  /* AMOUNT弹窗样式 */
  .amount-input-container {
    width: 100%;
  }
  
  .amount-display {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 16px;
  }
  
  .currency-symbol {
    font-size: 24px;
    font-weight: 500;
    color: #333;
    margin-right: 8px;
  }
  
  .amount-value-display {
    font-size: 24px;
    font-weight: 500;
    color: #333;
    min-width: 100px;
    text-align: left;
  }
  
  .amount-value {
    color: #F56C6C;
    font-weight: 500;
  }
  
  .status-owner-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .status-container {
    flex: 1;
  }
  
  .owner-container {
    flex-shrink: 0;
    margin-left: 12px;
  }
  
  .owner-name {
    margin-left: 8px;
    font-size: 14px;
    color: #333;
    vertical-align: middle;
  }
  
  
  
  </style>
  