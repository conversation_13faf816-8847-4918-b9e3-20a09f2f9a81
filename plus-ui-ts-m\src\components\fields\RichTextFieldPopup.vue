<template>
  <FieldPopupWrapper
    v-model:show="show"
    :title="field?.fieldName || '富文本编辑'"
    full-height
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <van-field
      v-model="localValue"
      type="textarea"
      :placeholder="`请输入${field.fieldName}`"
      :rows="10"
      autosize
      ref="fieldRef"
      class="field-textarea"
    />
  </FieldPopupWrapper>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import type { PageFieldVo } from '@/api/field/types'
import FieldPopupWrapper from './FieldPopupWrapper.vue'

interface Props {
  show: boolean
  field: PageFieldVo
  modelValue: string
}

const props = defineProps<Props>()

const emit = defineEmits<{ 
  (e: 'update:show', value: boolean): void,
  (e: 'update:modelValue', value: string): void,
  (e: 'confirm', value: string): void,
  (e: 'cancel'): void
}>()

// base64解码函数
const decodeBase64 = (base64String: string): string => {
  try {
    // 检查是否为有效的base64字符串
    if (!base64String || typeof base64String !== 'string') {
      return base64String
    }
    
    // 尝试解码base64
    return atob(base64String)
  } catch (error) {
    console.warn('Base64解码失败:', error)
    // 如果解码失败，返回原始字符串
    return base64String
  }
}

const localValue = ref(decodeBase64(props.modelValue))
const fieldRef = ref<any>(null)

const show = defineModel<boolean>('show')

watch(() => props.show, (newVal) => {
  if (newVal) {
    localValue.value = props.modelValue
    // 自动聚焦
    nextTick(() => {
      if (fieldRef.value && fieldRef.value.focus) {
        fieldRef.value.focus()
      }
    })
  }
})

// base64编码函数
const encodeBase64 = (string: string): string => {
  try {
    if (!string || typeof string !== 'string') {
      return string
    }
    return btoa(string)
  } catch (error) {
    console.warn('Base64编码失败:', error)
    return string
  }
}

const handleConfirm = () => {
  // 对富文本字段进行base64编码后保存
  emit('confirm', encodeBase64(localValue.value))
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.field-textarea {
  margin-top: 16px;
  height: 100%;
}

.field-textarea :deep(.van-field__control) {
  height: 100%;
}
</style>