import { JSEncrypt } from 'jsencrypt'

/**
 * RSA 加密工具类
 */
export class RSAEncrypt {
  private publicKey: string
  private privateKey: string
  private encryptor: JSEncrypt
  private decryptor: JSEncrypt

  constructor() {
    this.publicKey = import.meta.env.VITE_APP_RSA_PUBLIC_KEY
    this.privateKey = import.meta.env.VITE_APP_RSA_PRIVATE_KEY
    this.encryptor = new JSEncrypt()
    this.decryptor = new JSEncrypt()
    
    this.encryptor.setPublicKey(this.publicKey)
    this.decryptor.setPrivateKey(this.privateKey)
  }

  /**
   * 加密数据
   * @param data 要加密的数据
   * @returns 加密后的数据
   */
  encrypt(data: string): string {
    if (!import.meta.env.VITE_APP_ENCRYPT) {
      return data
    }
    
    try {
      const encrypted = this.encryptor.encrypt(data)
      return encrypted || data
    } catch (error) {
      console.error('RSA 加密失败:', error)
      return data
    }
  }

  /**
   * 解密数据
   * @param data 要解密的数据
   * @returns 解密后的数据
   */
  decrypt(data: string): string {
    if (!import.meta.env.VITE_APP_ENCRYPT) {
      return data
    }
    
    try {
      const decrypted = this.decryptor.decrypt(data)
      return decrypted || data
    } catch (error) {
      console.error('RSA 解密失败:', error)
      return data
    }
  }

  /**
   * 加密请求数据
   * @param data 请求数据
   * @returns 加密后的请求数据
   */
  encryptRequest(data: any): any {
    if (!import.meta.env.VITE_APP_ENCRYPT) {
      return data
    }

    try {
      const jsonStr = JSON.stringify(data)
      const encrypted = this.encrypt(jsonStr)
      return {
        encrypted: true,
        data: encrypted
      }
    } catch (error) {
      console.error('请求数据加密失败:', error)
      return data
    }
  }

  /**
   * 解密响应数据
   * @param data 响应数据
   * @returns 解密后的响应数据
   */
  decryptResponse(data: any): any {
    if (!import.meta.env.VITE_APP_ENCRYPT || !data.encrypted) {
      return data
    }

    try {
      const decrypted = this.decrypt(data.data)
      return JSON.parse(decrypted)
    } catch (error) {
      console.error('响应数据解密失败:', error)
      return data
    }
  }
}

// 创建全局实例
export const rsaEncrypt = new RSAEncrypt() 