<template>
  <van-popup 
    v-model:show="show" 
    position="bottom" 
    :style="{ height: selectorHeight }"
    class="field-selector-popup"
  >
    <div class="field-selector-header">
      <div class="field-selector-title">选择{{ selectorTitle }}</div>
      <div>
        <van-button type="default" size="small" @click="resetSelection" style="margin-right: 10px;">重置</van-button>
        <van-button type="primary" size="small" @click="confirmSelection">确定</van-button>
      </div>
    </div>
    
    <div class="field-selector-content">
      <van-checkbox-group 
        v-model="selectedValues" 
        class="field-selector-checkbox-group"
      >
        <van-checkbox 
          v-for="item in options" 
          :key="item.value" 
          :name="item.value"
          class="field-selector-checkbox"
        >
          {{ item.label }}
        </van-checkbox>
      </van-checkbox-group>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { getStateList, getMemberList } from '@/api/item'
import type { stateVO, ProjectMemberVo } from '@/api/item/types'

interface Props {
  modelValue: boolean
  projectId: number
  selectorType: 'status' | 'user'
  initialSelected: string[]
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', selected: string[], selectedIds: number[]): void
  (e: 'openFilterPopup'): void
}

interface Option {
  label: string
  value: string
  id: number
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const show = ref(false)
const options = ref<Option[]>([])
const selectedValues = ref<string[]>([])

// 监听弹出层显示状态
watch(() => props.modelValue, (newVal) => {
  show.value = newVal
  if (newVal) {
    // 重新打开时，初始化选中值
    selectedValues.value = [...props.initialSelected]
    // 加载选项数据
    loadOptions()
  }
})

// 监听弹出层内部显示状态
watch(show, (newVal) => {
  emit('update:modelValue', newVal)
})

// 计算选择器标题
const selectorTitle = computed(() => {
  return props.selectorType === 'status' ? '状态' : '人员'
})

// 计算选择器高度
const selectorHeight = computed(() => {
  const baseHeight = 120
  const itemHeight = 50
  const itemCount = options.value.length
  
  const calculatedHeight = baseHeight + (itemCount * itemHeight)
  const maxHeight = 400
  const finalHeight = Math.min(calculatedHeight, maxHeight)
  
  return `${finalHeight}px`
})

// 加载选项数据
const loadOptions = async () => {
  try {
    if (props.selectorType === 'status') {
      // 加载状态选项
      const response = await getStateList(props.projectId)
      const states = (response as any).rows || (response as any).data || []
      options.value = states.map((state: stateVO) => ({
        label: state.stateName,
        value: state.stateName,
        id: state.stateId
      }))
    } else if (props.selectorType === 'user') {
      // 加载人员选项
      const response = await getMemberList(props.projectId)
      const members = (response as any).rows || (response as any).data || []
      options.value = members.map((member: ProjectMemberVo) => ({
        label: member.nickName,
        value: member.nickName,
        id: member.userId
      }))
    }
  } catch (error) {
    console.error(`加载${selectorTitle.value}选项失败:`, error)
  }
}

// 重置选择
const resetSelection = () => {
  selectedValues.value = []
}

// 确认选择
const confirmSelection = () => {
  // 获取选中的ID
  const selectedIds = selectedValues.value.map(name => {
    const option = options.value.find(opt => opt.value === name);
    return option ? option.id : -1;
  }).filter(id => id !== -1);
  
  emit('confirm', [...selectedValues.value], selectedIds)
  show.value = false
  // 通知父组件打开筛选条件弹出框
  emit('openFilterPopup')
}
</script>

<style scoped>
.field-selector-popup {
  display: flex;
  flex-direction: column;
}

.field-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.field-selector-title {
  font-size: 16px;
  color: #333;
}

.field-selector-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background-color: #f9f9f9;
}

.field-selector-checkbox-group {
  display: flex;
  flex-direction: column;
}

.field-selector-checkbox {
  margin-bottom: 8px;
  background-color: #fff;
  padding: 10px 15px;
  border-radius: 8px;
}
</style>