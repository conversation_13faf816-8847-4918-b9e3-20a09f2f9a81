<template>
  <div class="page-container">
    <!-- 顶部栏 -->
    <van-nav-bar title="个人中心" fixed />

    <!-- 个人信息头部 -->
    <div class="profile-card">
      <div class="profile-header">
        <div class="profile-avatar">
          <img 
            src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=150&h=150&q=80" 
            alt="用户头像"
          />
        </div>
        <div class="profile-info">
          <h2 class="profile-name">张三</h2>
          <p class="profile-role">产品部 · 产品经理</p>
        </div>
      </div>
    </div>


    <!-- 菜单区 -->
    <div class="menu-card">
      <div class="menu-list">
        <div class="menu-item">
          <van-icon name="user-o" class="menu-icon" />
          <span class="menu-title">个人信息</span>
          <van-icon name="arrow" class="menu-arrow" />
        </div>
        <div class="menu-item">
          <van-icon name="info-o" class="menu-icon" />
          <span class="menu-title">关于</span>
          <span class="menu-value">v1.0.0</span>
        </div>
        <div class="menu-item">
          <van-icon name="shield-o" class="menu-icon" />
          <span class="menu-title">隐私政策</span>
          <van-icon name="arrow" class="menu-arrow" />
        </div>
      </div>
    </div>

    <!-- 退出按钮 -->
    <div class="logout-section">
      <van-button block type="primary" class="logout-btn" @click="onLogout">退出登录</van-button>
    </div>

    <!-- 底部导航栏 -->
    <van-tabbar route fixed>
      <van-tabbar-item to="/home" icon="home-o">首页</van-tabbar-item>
      <van-tabbar-item to="/projects" icon="apps-o">项目</van-tabbar-item>
      <van-tabbar-item to="/reports" icon="chart-trending-o">报表</van-tabbar-item>
      <van-tabbar-item to="/profile" icon="user-o">我的</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup lang="ts">
// 由于使用了 unplugin-vue-components，vant 组件已全局可用
// showToast 函数在 main.ts 中已全局注册
function onLogout() {
  // 使用全局的 showToast
  if (typeof window !== 'undefined' && (window as any).showToast) {
    (window as any).showToast('已退出登录')
  } else {
    alert('已退出登录')
  }
  setTimeout(() => {
    window.location.href = '/login'
  }, 800)
}
</script>

<style scoped>
.page-container {
  padding-top: 46px;
  padding-bottom: 50px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.profile-card {
  background-color: #fff;
  margin: 15px;
  border-radius:6px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  overflow: hidden;
}

.profile-header {
  display: flex;
  align-items: center;
  padding: 20px;
}

.profile-avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 16px;
  position: relative;
}

.profile-avatar::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0.3),
    rgba(255, 255, 255, 0)
  );
  transform: rotate(30deg);
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 20px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.profile-role {
  font-size: 14px;
  color: #999;
}

.menu-card {
  background-color: #fff;
  margin: 0 15px 15px 15px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  overflow: hidden;
}

.menu-list {
  padding: 0 20px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  font-size: 20px;
  color: #666;
  margin-right: 12px;
}

.menu-title {
  flex: 1;
  font-size: 16px;
  color: #333;
}

.menu-value {
  font-size: 14px;
  color: #999;
}

.menu-arrow {
  font-size: 16px;
  color: #ccc;
}

.logout-section {
  margin: 0 15px 15px 15px;
}

.logout-btn {
  height: 44px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 16px;
}
</style> 