<template>
  <div class="work-order-list">
    <!-- 搜索框 -->
    <div class="search-container">
      <van-search 
        v-model="search" 
        placeholder="搜索工单..." 
        class="search-input"
      />
      <van-button 
        icon="filter-o" 
        class="filter-btn" 
        @click="showFilterPopup = true"
      />
    </div>

    <!-- 工单列表 -->
    <div class="list-container" ref="listContainerRef">
      <van-list 
        v-model:loading="loading" 
        :finished="finished" 
        finished-text="没有更多了"
        @load="loadMore"
      >
         <van-cell 
          v-for="item in displayList" 
          :key="item.itemId" 
          :to="{
            path: '/detail',
            query: {
              projectId: props.projectId,
              itemId: item.itemId,
              typeId: getFieldChoiceId(item, 5)
            }
          }" 
          class="list-item"
        >
          <template #title>
            <div class="list-item-header">
              <span class="list-item-title">{{ getFieldValue(item, 1) }}</span>
            </div>
          </template>
          <template #label>
            <div class="list-item-no">工单号: {{ item.displayId }}</div>
            <div class="list-item-desc">{{ getFieldValue(item, 2) }}</div>
            <div class="list-item-footer">
              <van-tag 
                :style="{
                  backgroundColor: getStatusBackgroundColor(item),
                  color: getStatusColor(item),
                  // border: `1px solid ${getStatusColor(item)}`
                }"
                class="status-tag">
                {{ getFieldValue(item, 3) }}
              </van-tag>
              <div class="list-item-user">
                <img :src="getUserAvatar(getFieldValue(item, 4))" class="avatar-img" />
                <span style="margin-left: 5px;">{{ getFieldValue(item, 4) }}</span>
              </div>
            </div>
          </template>
        </van-cell>
      </van-list>
    </div>

    <!-- 过滤弹出层 -->
    <van-popup 
      v-model:show="showFilterPopup" 
      round
      position="bottom" 
      :style="{ height: '25%' }"
      class="filter-popup"
    >
      <div class="filter-header">
        <div class="filter-title">筛选条件</div>
        <div>
          <van-button type="default" size="small" @click="resetFilter" style="margin-right: 10px;">重置</van-button>
          <!-- <van-button type="primary" size="small" @click="applyFilter">确定</van-button> -->
        </div>
      </div>
      
      <div class="filter-content">
        <van-cell-group>
          <van-cell title="状态" is-link @click="openFieldSelector('status')">
            <template #value>
              <span class="filter-value">
                {{ selectedStatuses.length > 0 ? selectedStatuses.join(', ') : '请选择' }}
              </span>
            </template>
          </van-cell>
          <van-cell title="人员" is-link @click="openFieldSelector('user')">
            <template #value>
              <span class="filter-value">
                {{ selectedUsers.length > 0 ? selectedUsers.join(', ') : '请选择' }}
              </span>
            </template>
          </van-cell>
        </van-cell-group>
      </div>
    </van-popup>

    <!-- 状态选择弹出层 -->
    <FieldSelectorPopup
      v-model="showStatusSelector"
      :project-id="Number(props.projectId)"
      selector-type="status"
      :initial-selected="selectedStatuses"
      @confirm="handleStatusConfirm"
      @openFilterPopup="() => showFilterPopup = true"
    />

    <!-- 人员选择弹出层 -->
    <FieldSelectorPopup
      v-model="showUserSelector"
      :project-id="Number(props.projectId)"
      selector-type="user"
      :initial-selected="selectedUsers"
      @confirm="handleUserConfirm"
      @openFilterPopup="() => showFilterPopup = true"
    />


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue'
import { listItem, getStateList, getMemberList } from '@/api/item'
import type { ListItemVo, stateVO, ProjectMemberVo } from '@/api/item/types'
import { SystemFieldEnum } from '@/enums/STCommonEnum'
import FieldSelectorPopup from './FieldSelectorPopup.vue'

interface Props {
  projectId: string | number
}

const props = defineProps<Props>()

const search = ref('')
const showFilterPopup = ref(false)
const selectedStatuses = ref<string[]>([])
const selectedUsers = ref<string[]>([])
// 控制状态选择器显示
const showStatusSelector = ref(false)
// 控制人员选择器显示
const showUserSelector = ref(false)
const loading = ref(false)
const finished = ref(false)
const currentPage = ref(1)
const pageSize = 10
const allData = ref<ListItemVo[]>([])
const stateList = ref<stateVO[]>([])

// 应用过滤条件后的列表
const displayList = computed(() => {
  let result = allData.value
  
  // 应用搜索过滤
  if (search.value) {
    result = result.filter(item =>
      getFieldValue(item, 1).includes(search.value) ||
      item.displayId.includes(search.value) ||
      getFieldValue(item, 4).includes(search.value)
    )
  }
  
  // 应用状态过滤
  if (selectedStatuses.value.length > 0) {
    result = result.filter(item => selectedStatuses.value.includes(getFieldValue(item, 3)))
  }
  
  // 应用人员过滤
  if (selectedUsers.value.length > 0) {
    result = result.filter(item => selectedUsers.value.includes(getFieldValue(item, 4)))
  }
  
  return result
})

// 获取字段值
const getFieldValue = (item: ListItemVo, fieldId: number): string => {
  const field = item.values.find(v => v.id === fieldId)
  return field?.value || ''
}

// 获取字段的choiceId
const getFieldChoiceId = (item: ListItemVo, fieldId: number): number | null => {
  const field = item.values.find(v => v.id === fieldId)
  if (field?.choiceId) {
    // 如果是数组，取第一个元素
    if (Array.isArray(field.choiceId)) {
      return field.choiceId[0] || null
    }
    return field.choiceId
  }
  return null
}

// 获取状态颜色
const getStatusColor = (item: ListItemVo): string => {
  const choiceId = getFieldChoiceId(item, SystemFieldEnum.STATE)
  if (choiceId && stateList.value.length > 0) {
    const state = stateList.value.find(s => s.stateId === choiceId)
    if (state?.stateColor) {
      return state.stateColor
    } else if (state?.stateOptionId === 0) {
      // 当stateOptionId为0时，返回#E6A23C
      return '#E6A23C'
    } else if (state?.stateOptionId === 1) {
      // 当stateOptionId为1时，返回#67C23A
      return '#67C23A'
    }
  }
}

// 生成浅色背景色
const getStatusBackgroundColor = (item: ListItemVo): string => {
  const color = getStatusColor(item)
  if (!color) return '#f0f0f0'
  
  // 如果是十六进制颜色
  if (color.startsWith('#')) {
    // 将十六进制颜色转换为RGB
    const hex = color.replace('#', '')
    const r = parseInt(hex.substr(0, 2), 16)
    const g = parseInt(hex.substr(2, 2), 16)
    const b = parseInt(hex.substr(4, 2), 16)
    
    // 生成浅色背景（添加白色混合，透明度为0.2）
    return `rgba(${r}, ${g}, ${b}, 0.2)`
  }
  
  // 如果是RGB颜色
  if (color.startsWith('rgb')) {
    const rgb = color.match(/\d+/g)
    if (rgb && rgb.length >= 3) {
      const r = parseInt(rgb[0])
      const g = parseInt(rgb[1])
      const b = parseInt(rgb[2])
      return `rgba(${r}, ${g}, ${b}, 0.2)`
    }
  }
  
  // 默认返回浅灰色
  return '#f0f0f0'
}

// 获取用户头像
const getUserAvatar = (userName: string): string => {
  // 这里可以根据用户名生成头像，或者使用默认头像
  return `https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80`
}

// 加载更多数据
const loadMore = async () => {
  if (loading.value || finished.value) return
  
  loading.value = true
  try {
    const response = await listItem({
      projectId: props.projectId,
      stateIds: [-1],
      ownerIds: [0],
      fieldIds: [1, 2, 3, 4, 5],
      sortFieldId: -10,
      pageNum: currentPage.value,
      pageSize: pageSize
    })
    
    // 根据实际响应结构获取数据
    const newData = (response as any).rows || (response as any).data || []
    allData.value.push(...newData)
    
    if (newData.length < pageSize) {
      finished.value = true
    } else {
      currentPage.value++
    }
  } catch (error) {
    console.error('加载工单列表失败:', error)
    // 显示错误提示
    import('vant').then(({ showToast }) => {
      showToast('加载失败，请重试')
    })
  } finally {
    loading.value = false
  }
}

// 获取状态列表
const loadStateList = async () => {
  try {
    const response = await getStateList(Number(props.projectId))
    // 根据实际响应结构获取数据
    stateList.value = (response as any).rows || (response as any).data || []
  } catch (error) {
    console.error('加载状态列表失败:', error)
  }
}

// 打开字段选择器
const openFieldSelector = (field: string) => {
  showFilterPopup.value = false
  if (field === 'status') {
    showStatusSelector.value = true
  } else if (field === 'user') {
    showUserSelector.value = true
  }
}

// 处理状态选择确认
const handleStatusConfirm = async (selected: string[], selectedIds: number[]) => {
  selectedStatuses.value = selected
  // 调用listItem接口更新数据
  await refreshListWithFilters(selectedIds, selectedUsers.value)
}

// 处理人员选择确认
const handleUserConfirm = async (selected: string[], selectedIds: number[]) => {
  selectedUsers.value = selected
  // 调用listItem接口更新数据
  await refreshListWithFilters(selectedStatuses.value, selectedIds)
}

// 刷新列表数据
const refreshList = () => {
  // 重置分页
  allData.value = []
  currentPage.value = 1
  finished.value = false
  // 重新加载数据
  loadMore()
}

// 根据筛选条件刷新列表数据
const refreshListWithFilters = async (stateIds: number[], ownerIds: number[]) => {
  // 重置分页
  allData.value = []
  currentPage.value = 1
  finished.value = false
  
  // 调用listItem接口获取数据
  try {
    const response = await listItem({
      projectId: props.projectId,
      stateIds: stateIds.length > 0 ? stateIds : [-1],
      ownerIds: ownerIds.length > 0 ? ownerIds : [0],
      fieldIds: [1, 2, 3, 4, 5],
      sortFieldId: -10,
      pageNum: currentPage.value,
      pageSize: pageSize
    })
    
    // 根据实际响应结构获取数据
    const newData = (response as any).rows || (response as any).data || []
    allData.value = newData
    
    if (newData.length < pageSize) {
      finished.value = true
    } else {
      currentPage.value++
    }
  } catch (error) {
    console.error('加载工单列表失败:', error)
    // 显示错误提示
    import('vant').then(({ showToast }) => {
      showToast('加载失败，请重试')
    })
  }
}

// 应用过滤条件
const applyFilter = () => {
  showFilterPopup.value = false
  refreshList()
}

// 重置过滤条件
const resetFilter = async () => {
  selectedStatuses.value = []
  selectedUsers.value = []
  // 重新加载数据，传入-1表示无过滤条件
  await refreshListWithFilters([-1], [0])
}

// 监听项目ID变化，重新加载数据
watch(() => props.projectId, () => {
  allData.value = []
  currentPage.value = 1
  finished.value = false
  loadStateList() // 重新加载状态列表
  loadMore()
}, { immediate: true })

// 滚动相关逻辑
const listContainerRef = ref<HTMLElement | null>(null)
let scrollTimer: number | null = null

const handleScroll = () => {
  if (listContainerRef.value) {
    listContainerRef.value.classList.add('scrolling')
    
    if (scrollTimer) {
      clearTimeout(scrollTimer)
    }
    
    scrollTimer = window.setTimeout(() => {
      if (listContainerRef.value) {
        listContainerRef.value.classList.remove('scrolling')
      }
      scrollTimer = null
    }, 1000)
  }
}

onMounted(() => {
  if (listContainerRef.value) {
    listContainerRef.value.addEventListener('scroll', handleScroll)
  }
  loadStateList()
})

onBeforeUnmount(() => {
  if (listContainerRef.value) {
    listContainerRef.value.removeEventListener('scroll', handleScroll)
  }
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }
})
</script>

<style scoped>
.work-order-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-container {
  display: flex;
  align-items: center;
  padding: 0 10px;
  background-color: #f5f5f5;
  height: 50px;
  margin: 12px 0;
}

.search-input {
  flex: 1;
}

.search-input :deep(.van-search) {
  background-color: #fff !important;
}

.filter-btn {
  width: 40px;
  height: 50px;
  border: none;
}

.list-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.list-item-title {
  font-weight: bold;
  font-size: 15px;
}

.status-tag {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  display: inline-block;
  text-align: center;
  min-width: 60px;
}

.list-item-no {
  color: #a2a3a7;
  font-size: 14px;
  margin-bottom: 8px;
}

.list-item-desc {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.list-item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #9ca3af;
}

.list-item-user {
  display: flex;
  align-items: center;
}

.list-container {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 0 10px 50px 10px;
  max-height: calc(100vh - 46px - 50px - 50px);
  scrollbar-width: thin;
  -ms-overflow-style: none;
  scrollbar-color: transparent transparent;
}

.list-container.scrolling {
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
}

.list-container::-webkit-scrollbar {
  width: 8px;
  background: transparent;
}

.list-container::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 4px;
}

.list-container.scrolling::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
}

.list-item {
  margin-bottom: 8px;
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
}

.avatar-img {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  margin-left: 4px;
}

.filter-popup {
  display: flex;
  flex-direction: column;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.filter-title {
  font-size: 16px;
  color: #333;
}

.filter-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background-color: #f9f9f9;
}

.filter-value {
  color: #999;
}

.field-selector-popup {
  display: flex;
  flex-direction: column;
}

.field-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.field-selector-title {
  font-size: 16px;
  color: #333;
}

.field-selector-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background-color: #f9f9f9;
}

.field-selector-checkbox-group {
  display: flex;
  flex-direction: column;
}

.field-selector-checkbox {
  margin-bottom: 8px;
  background-color: #fff;
  padding: 10px 15px;
  border-radius: 8px;
}

.van-search {
  border-radius: 4px;
}
</style>