<template>
  <div class="announcement-card">
    <div class="section-header">系统公告</div>
    <div v-if="loading" class="loading">
      <van-loading type="spinner" color="#409EFF" />
      <p>加载中...</p>
    </div>
    <div v-else-if="error" class="error">
      <p>{{ error }}</p>
      <van-button type="primary" size="small" @click="fetchAnnouncements">重试</van-button>
    </div>
    <div v-else class="announcement-list">
      <div 
        class="announcement-item" 
        v-for="(announcement, index) in announcements" 
        :key="announcement.noticeId"
        @click="showAnnouncementDetail(announcement)"
      >
        <div class="announcement-header">
          <span class="announcement-title">{{ announcement.noticeTitle }}</span>
          <span class="announcement-date">{{ formatDate(announcement.createTime || '') }}</span>
        </div>
        <div class="announcement-content">
          {{ getPlainText(announcement.noticeContent) }}
        </div>
      </div>
    </div>

    <!-- 公告详情弹出层 -->
    <van-popup v-model:show="showDetail" position="right" :style="{ width: '100%', height: '100%' }">
      <div class="announcement-detail">
        <van-nav-bar
          title="系统公告"
          left-arrow
          @click-left="showDetail = false"
        />
        <div class="detail-content">
          <div class="detail-header">
            <h2 class="detail-title">{{ selectedAnnouncement.noticeTitle }}</h2>
            <div class="detail-meta">
              <span class="detail-date">{{ formatDate(selectedAnnouncement.createTime || '') }}</span>
            </div>
          </div>
          <div class="detail-body">
            <div class="detail-content" v-html="selectedAnnouncement.noticeContent"></div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { listNotice } from '@/api/general/index';
import type { NoticeVO } from '@/api/general/types';

// 公告相关数据
const announcements = ref<NoticeVO[]>([]);
const loading = ref(true);
const error = ref('');
const showDetail = ref(false);
const selectedAnnouncement = ref<NoticeVO>({
  noticeId: 0,
  noticeTitle: '',
  noticeType: '',
  noticeContent: '',
  status: '',
  remark: '',
  createByName: '',
  createTime: ''
});

const fetchAnnouncements = async () => {
  try {
    loading.value = true;
    error.value = '';
    const response = await listNotice();
    announcements.value = response.rows || [];
  } catch (err) {
    console.error('获取公告数据失败:', err);
    error.value = '获取公告数据失败，请重试';
  } finally {
    loading.value = false;
  }
};

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};

// 从HTML内容中提取纯文本
const getPlainText = (htmlContent: string) => {
  if (!htmlContent) return '';
  // 创建临时DOM元素来解析HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlContent;
  const text = tempDiv.textContent || tempDiv.innerText || '';
  // 限制显示长度
  return text.length > 100 ? text.substring(0, 100) + '...' : text;
};

// 显示公告详情
const showAnnouncementDetail = (announcement: NoticeVO) => {
  selectedAnnouncement.value = announcement;
  showDetail.value = true;
};

onMounted(() => {
  fetchAnnouncements();
});
</script>

<style scoped>
.announcement-card {
  background-color: #fff;
  margin: 0 15px 15px 15px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  overflow: hidden;
}

.section-header {
  padding: 15px 15px 10px;
  font-weight: 500;
  font-size: 16px;
  color: #333;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
}

.loading p {
  margin-top: 10px;
  font-size: 14px;
}

.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #F56C6C;
}

.error p {
  margin-bottom: 15px;
  font-size: 14px;
}

.announcement-list {
  padding: 0 15px 15px;
}

.announcement-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}

.announcement-item:last-child {
  border-bottom: none;
}

.announcement-item:active {
  background-color: #f9f9f9;
}

.announcement-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.important-tag {
  color: #F56C6C;
  font-weight: bold;
  margin-right: 8px;
}

.announcement-title {
  font-weight: 500;
  color: #333;
  flex: 1;
}

.announcement-date {
  font-size: 12px;
  color: #999;
}

.announcement-content {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 公告详情样式 */
.announcement-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  font-size: 15px;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.detail-header {
  margin: 0 10px 20px 10px;
}

.detail-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.detail-meta {
  display: flex;
  align-items: center;
}

.detail-date {
  font-size: 14px;
  color: #999;
  margin-left: 8px;
}

.detail-body {
  background-color: #fff;
  border-radius: 6px;
  padding: 5px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* HTML内容样式 */
.detail-content :deep(h1),
.detail-content :deep(h2),
.detail-content :deep(h3),
.detail-content :deep(h4),
.detail-content :deep(h5),
.detail-content :deep(h6) {
  margin: 16px 0 8px 0;
  font-weight: bold;
  color: #333;
}

.detail-content :deep(p) {
  margin: 2px 0;
  line-height: 1.6;
}

.detail-content :deep(ul),
.detail-content :deep(ol) {
  margin: 8px 0;
  padding-left: 20px;
}

.detail-content :deep(li) {
  margin: 4px 0;
}

.detail-content :deep(strong),
.detail-content :deep(b) {
  font-weight: bold;
}

.detail-content :deep(em),
.detail-content :deep(i) {
  font-style: italic;
}

.detail-content :deep(a) {
  color: #409EFF;
  text-decoration: none;
}

.detail-content :deep(a:hover) {
  text-decoration: underline;
}
</style> 