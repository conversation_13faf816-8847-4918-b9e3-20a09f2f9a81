<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工单列表 - ITSM系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background-color: #f5f7fa;
            width: 100%;
            max-width: 430px;
            margin: 0 auto;
            height: 100vh;
            overflow: hidden;
            position: relative;
            border-radius: 40px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
        }
        
        .app-container {
            width: 100%;
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            background-color: white;
            border-radius: 40px;
        }
        
        .status-bar {
            height: 44px;
            width: 100%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            position: relative;
            z-index: 10;
            border-top-left-radius: 40px;
            border-top-right-radius: 40px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .tab-bar {
            height: 84px;
            width: 100%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-around;
            border-top: 1px solid rgba(0,0,0,0.05);
            padding-bottom: 20px;
            border-bottom-left-radius: 40px;
            border-bottom-right-radius: 40px;
        }
        
        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 50px;
            color: #999;
        }
        
        .tab-item.active {
            color: #409EFF;
        }
        
        .content {
            flex: 1;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            padding: 15px;
            background-color: #f5f7fa;
        }
        
        .card {
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        .badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .badge-primary {
            background-color: rgba(64, 158, 255, 0.1);
            color: #409EFF;
        }
        
        .badge-warning {
            background-color: rgba(230, 162, 60, 0.1);
            color: #E6A23C;
        }
        
        .badge-danger {
            background-color: rgba(245, 108, 108, 0.1);
            color: #F56C6C;
        }
        
        .badge-success {
            background-color: rgba(103, 194, 58, 0.1);
            color: #67C23A;
        }
        
        .fab-button {
            position: fixed;
            bottom: 100px;
            right: 30px;
            width: 56px;
            height: 56px;
            border-radius: 28px;
            background-color: #409EFF;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
            z-index: 100;
        }
        
        .filter-button {
            background-color: transparent;
            border: none;
            color: #409EFF;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="status-bar">
            <h1 class="text-lg font-bold">工单列表</h1>
            <button class="filter-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
                </svg>
            </button>
        </div>
        
        <div class="content">
            <!-- 搜索框 -->
            <div class="relative mb-4">
                <input type="text" placeholder="搜索工单..." class="w-full bg-white rounded-lg border border-gray-200 pl-10 pr-4 py-2 text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="absolute left-3 top-2.5 text-gray-400">
                    <circle cx="11" cy="11" r="8"></circle>
                    <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                </svg>
            </div>
            
            <!-- 工单列表 -->
            <div class="card">
                <a href="work-order-detail.html" class="block p-3 border-b border-gray-100">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <h4 class="text-base font-medium mb-1">网络连接问题</h4>
                            <div class="text-xs text-gray-500">工单号: WO-2023042501</div>
                        </div>
                        <span class="badge badge-warning">处理中</span>
                    </div>
                    <div class="text-sm text-gray-600 mb-2 line-clamp-2">无法连接到公司内网，已经重启路由器但问题仍然存在</div>
                    <div class="flex justify-between items-center text-xs text-gray-500">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12 6 12 12 16 14"></polyline>
                            </svg>
                            <span>2023-04-25 14:30</span>
                        </div>
                        <div class="flex items-center">
                            <span>张三</span>
                            <div class="w-5 h-5 rounded-full bg-gray-300 ml-1 flex items-center justify-center overflow-hidden">
                                <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=30&h=30&q=80" alt="用户头像">
                            </div>
                        </div>
                    </div>
                </a>
                
                <a href="work-order-detail.html" class="block p-3 border-b border-gray-100">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <h4 class="text-base font-medium mb-1">账户权限申请</h4>
                            <div class="text-xs text-gray-500">工单号: WO-2023042502</div>
                        </div>
                        <span class="badge badge-primary">待审批</span>
                    </div>
                    <div class="text-sm text-gray-600 mb-2 line-clamp-2">申请财务系统的只读权限，用于查看部门预算执行情况</div>
                    <div class="flex justify-between items-center text-xs text-gray-500">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12 6 12 12 16 14"></polyline>
                            </svg>
                            <span>2023-04-25 10:15</span>
                        </div>
                        <div class="flex items-center">
                            <span>李四</span>
                            <div class="w-5 h-5 rounded-full bg-gray-300 ml-1 flex items-center justify-center overflow-hidden">
                                <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=30&h=30&q=80" alt="用户头像">
                            </div>
                        </div>
                    </div>
                </a>
                
                <a href="work-order-detail.html" class="block p-3 border-b border-gray-100">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <h4 class="text-base font-medium mb-1">软件安装请求</h4>
                            <div class="text-xs text-gray-500">工单号: WO-2023042503</div>
                        </div>
                        <span class="badge badge-success">已完成</span>
                    </div>
                    <div class="text-sm text-gray-600 mb-2 line-clamp-2">请求在办公电脑上安装 Adobe Photoshop 软件，用于设计工作</div>
                    <div class="flex justify-between items-center text-xs text-gray-500">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12 6 12 12 16 14"></polyline>
                            </svg>
                            <span>2023-04-24 16:20</span>
                        </div>
                        <div class="flex items-center">
                            <span>王五</span>
                            <div class="w-5 h-5 rounded-full bg-gray-300 ml-1 flex items-center justify-center overflow-hidden">
                                <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=30&h=30&q=80" alt="用户头像">
                            </div>
                        </div>
                    </div>
                </a>
                
                <a href="work-order-detail.html" class="block p-3">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <h4 class="text-base font-medium mb-1">VPN连接配置</h4>
                            <div class="text-xs text-gray-500">工单号: WO-2023042505</div>
                        </div>
                        <span class="badge badge-success">已完成</span>
                    </div>
                    <div class="text-sm text-gray-600 mb-2 line-clamp-2">请求设置远程办公VPN连接，以便在家办公时能够访问公司内部系统</div>
                    <div class="flex justify-between items-center text-xs text-gray-500">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12 6 12 12 16 14"></polyline>
                            </svg>
                            <span>2023-04-23 11:30</span>
                        </div>
                        <div class="flex items-center">
                            <span>孙七</span>
                            <div class="w-5 h-5 rounded-full bg-gray-300 ml-1 flex items-center justify-center overflow-hidden">
                                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=30&h=30&q=80" alt="用户头像">
                            </div>
                        </div>
                    </div>
                </a>
            </div>
            
            <!-- 悬浮新建按钮 -->
            <a href="create-work-order.html" class="fab-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
            </a>
        </div>
        
        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <a href="home.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                    <polyline points="9 22 9 12 15 12 15 22"></polyline>
                </svg>
                <span class="text-xs mt-1">首页</span>
            </a>
            <a href="modules.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="3" y="3" width="7" height="7"></rect>
                    <rect x="14" y="3" width="7" height="7"></rect>
                    <rect x="14" y="14" width="7" height="7"></rect>
                    <rect x="3" y="14" width="7" height="7"></rect>
                </svg>
                <span class="text-xs mt-1">模块</span>
            </a>
            <a href="reports.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="12" y1="20" x2="12" y2="10"></line>
                    <line x1="18" y1="20" x2="18" y2="4"></line>
                    <line x1="6" y1="20" x2="6" y2="16"></line>
                </svg>
                <span class="text-xs mt-1">报表</span>
            </a>
            <a href="profile.html" class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                </svg>
                <span class="text-xs mt-1">我的</span>
            </a>
        </div>
    </div>
    
    <script>
        // 点击过滤按钮的事件处理
        document.querySelector('.filter-button').addEventListener('click', function() {
            // 这里可以添加过滤功能的实现
            alert('过滤功能即将开放');
        });
    </script>
</body>
</html>
