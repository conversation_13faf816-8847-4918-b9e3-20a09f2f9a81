<template>
  <div class="service-list">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div v-if="services.length === 0 && !loading" class="empty-state">
          <van-icon name="apps-o" class="empty-state-icon" />
          <p>暂无服务</p>
        </div>
        
        <div v-else class="service-grid">
          <div
            v-for="service in services"
            :key="service.id"
            class="service-item card"
            @click="applyService(service)"
          >
            <div class="service-icon">
              <van-icon :name="service.icon" size="32" :color="service.color" />
            </div>
            <div class="service-info">
              <h3 class="service-title">{{ service.name }}</h3>
              <p class="service-desc">{{ service.description }}</p>
              <div class="service-meta">
                <van-tag :type="service.status === 'available' ? 'success' : 'default'">
                  {{ service.status === 'available' ? '可用' : '维护中' }}
                </van-tag>
                <span class="service-time">{{ service.responseTime }}</span>
              </div>
            </div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

interface Service {
  id: string
  name: string
  description: string
  category: string
  status: string
  responseTime: string
  icon: string
  color: string
}

const props = defineProps<{
  category: string
  search: string
}>()

const router = useRouter()
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const services = ref<Service[]>([])

// 模拟数据
const mockServices: Service[] = [
  {
    id: '1',
    name: '电脑维修',
    description: '台式机、笔记本硬件故障维修',
    category: 'device',
    status: 'available',
    responseTime: '2小时内响应',
    icon: 'desktop-o',
    color: '#1989fa'
  },
  {
    id: '2',
    name: '打印机维护',
    description: '打印机故障排除和日常维护',
    category: 'device',
    status: 'available',
    responseTime: '4小时内响应',
    icon: 'printer-o',
    color: '#07c160'
  },
  {
    id: '3',
    name: '软件安装',
    description: '办公软件、专业软件安装配置',
    category: 'software',
    status: 'available',
    responseTime: '1小时内响应',
    icon: 'setting-o',
    color: '#ff976a'
  },
  {
    id: '4',
    name: '网络配置',
    description: '网络连接、WiFi配置服务',
    category: 'network',
    status: 'maintenance',
    responseTime: '2小时内响应',
    icon: 'wifi-o',
    color: '#7232dd'
  },
  {
    id: '5',
    name: '数据恢复',
    description: '硬盘数据恢复和备份服务',
    category: 'software',
    status: 'available',
    responseTime: '24小时内响应',
    icon: 'replay',
    color: '#ee0a24'
  },
  {
    id: '6',
    name: '设备采购',
    description: 'IT设备采购和配置服务',
    category: 'device',
    status: 'available',
    responseTime: '3个工作日内响应',
    icon: 'shopping-cart-o',
    color: '#1989fa'
  }
]

// 加载数据
const loadData = () => {
  // 模拟API调用
  setTimeout(() => {
    let filteredServices = mockServices
    
    // 按分类筛选
    if (props.category !== 'all') {
      filteredServices = mockServices.filter(service => service.category === props.category)
    }
    
    // 按搜索关键词筛选
    if (props.search) {
      filteredServices = filteredServices.filter(service => 
        service.name.toLowerCase().includes(props.search.toLowerCase()) ||
        service.description.toLowerCase().includes(props.search.toLowerCase())
      )
    }
    
    services.value = filteredServices
    loading.value = false
    finished.value = true
  }, 1000)
}

// 下拉刷新
const onRefresh = () => {
  finished.value = false
  loadData()
  refreshing.value = false
}

// 上拉加载
const onLoad = () => {
  loadData()
}

// 申请服务
const applyService = (service: Service) => {
  if (service.status === 'maintenance') {
    showToast('该服务正在维护中，请稍后再试')
    return
  }
  
  // 跳转到创建工单页面，并传递服务信息
  router.push({
    path: '/work-order/create',
    query: {
      serviceId: service.id,
      serviceName: service.name
    }
  })
}

// 监听搜索和分类变化
watch([() => props.category, () => props.search], () => {
  loadData()
})

onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.service-list {
  .service-grid {
    .service-item {
      display: flex;
      align-items: center;
      gap: 16px;
      cursor: pointer;
      transition: transform 0.2s;

      &:active {
        transform: scale(0.98);
      }

      .service-icon {
        flex-shrink: 0;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--background-color);
        border-radius: 12px;
      }

      .service-info {
        flex: 1;

        .service-title {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
          color: var(--text-color);
        }

        .service-desc {
          margin: 0 0 8px 0;
          font-size: 14px;
          color: var(--text-color-2);
          line-height: 1.4;
        }

        .service-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .service-time {
            font-size: 12px;
            color: var(--text-color-3);
          }
        }
      }
    }
  }
}
</style> 