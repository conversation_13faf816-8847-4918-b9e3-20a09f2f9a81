# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

ITSM (IT Service Management) mobile web application built with Vue 3.5 + TypeScript + Vant 4 for mobile-first service management.

## Key Architecture

### Technology Stack
- **Framework**: Vue 3.5 + TypeScript + Composition API
- **UI**: Vant 4 (mobile-optimized components)
- **State Management**: Pinia 3
- **Routing**: Vue Router 4
- **Build**: Vite 5
- **Testing**: Vitest + jsdom
- **Styling**: SCSS + postcss-px-to-viewport (375px design)

### Project Structure
```
src/
├── api/           # API layer with type definitions
├── components/    # Reusable components (auto-imported)
├── stores/        # Pinia stores (user state management)
├── views/         # Page components
│   ├── home/      # Dashboard
│   ├── incident/  # Incident management
│   ├── profile/   # User profile
│   ├── project/   # Project management
│   └── report/    # Analytics/reporting
├── utils/         # Utility functions (auth, crypto, request)
├── styles/        # Global styles
└── App.vue        # Root component
```

### Core Patterns

#### API Integration
- Centralized in `src/api/` with TypeScript interfaces
- Request/response encryption support via `VITE_APP_ENCRYPT`
- Automatic token injection via `src/utils/request.ts`
- Anti-replay attack protection

#### State Management
- `useUserStore` handles authentication, user info, and project context
- Token-based authentication with automatic refresh
- Multi-tenant support via tenantId

#### Mobile Optimization
- Viewport-based responsive design (375px base)
- Touch-friendly Vant components
- PostCSS px-to-vw conversion
- CSS variables for theming

## Development Commands

### Essential Scripts
```bash
# Development
npm run dev                    # Start dev server
npm run build:prod            # Production build
npm run preview:prod          # Preview production build

# Quality
npm run lint                  # ESLint fix
npm run type-check            # TypeScript checking
npm run test                  # Run tests
npm run test:ui               # UI test runner

# Analysis
npm run analyze               # Bundle size analysis
```

### Environment Configuration
- `.env.development` - Local development
- `.env.production` - Production settings
- `.env.preview` - Preview environment
- All client vars must use `VITE_` prefix

### Key Environment Variables
- `VITE_API_BASE_URL` - Backend API URL
- `VITE_APP_ENCRYPT` - Enable request encryption
- `VITE_APP_CLIENT_ID` - Client authentication
- `VITE_BASE_URL` - App base path

## Code Patterns

### API Usage
```typescript
// Auto-imported: no need to import request
const data = await request.get('/api/endpoint')
const result = await request.post('/api/endpoint', payload)
```

### Store Usage
```typescript
const userStore = useUserStore()
await userStore.login(credentials)
const isAuthenticated = computed(() => !!userStore.token)
```

### Component Creation
- Components in `src/components/` are auto-imported via unplugin-vue-components
- Use Composition API with `<script setup lang="ts">`
- Follow Vant mobile-first design patterns

### Styling
- Use SCSS with BEM methodology
- px units auto-convert to vw for mobile
- Theme colors via CSS variables
- Font: AlibabaPuHuiTi

## Testing
- Vitest with jsdom environment
- Tests in `src/**/__tests__/`
- Vue Test Utils for component testing
- Global test environment configured

## Build & Deployment
- Output: `dist/` directory
- Chunk splitting for vendor, Vant, utilities
- Source maps in development
- Production optimizations: minification, tree-shaking
- Gzip/Brotli compression ready

## Mobile-Specific Features
- 375px design viewport
- Touch gestures via Vant
- Responsive images
- Mobile-optimized navigation
- Pull-to-refresh patterns

## Security
- JWT token authentication
- Optional request/response encryption
- CSRF protection
- Input validation
- XSS prevention via Vue templating