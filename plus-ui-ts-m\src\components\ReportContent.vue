<template>
  <div class="content" ref="contentRef">
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <div class="category-list">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <van-loading type="spinner" color="#409EFF" />
        <div class="loading-text">加载中...</div>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <div class="error-icon">⚠️</div>
        <div class="error-text">{{ error }}</div>
        <van-button type="primary" size="small" @click="loadData">重试</van-button>
      </div>
      
      <!-- 数据内容 -->
      <div v-else-if="reportData.length > 0">
        <div 
          v-for="category in reportData" 
          :key="category.childFolder"
          class="category-item" 
          :class="{ expanded: expandedCategory === category.childFolder }"
        >
          <div class="category-header" @click="toggleCategory(category.childFolder)">
            <div class="category-icon" :style="{ backgroundColor: getCategoryColor(category.childFolder) }">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#409EFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                <polyline points="14 2 14 8 20 8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10 9 9 9 8 9"></polyline>
              </svg>
            </div>
            <div class="category-info">
              <div class="category-title">{{ category.childFolderName }}</div>
              <div class="category-desc">{{ getCategoryDescription(category.childFolder) }}</div>
            </div>
            <div class="report-count">{{ getReportCount(category) }}个报表</div>
            <div class="expand-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </div>
          </div>
          <div class="category-content">
            <div 
              v-for="report in getReportsFromCategory(category)" 
              :key="report.childFolder"
              class="report-item"
              @click="handleReportClick(report)"
            >
              <div class="report-icon">
                <svg v-if="report.childFolderType === -1" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#409EFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                  <polyline points="14 2 14 8 20 8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                  <polyline points="10 9 9 9 8 9"></polyline>
                </svg>
                <svg v-else-if="report.childFolderType === -2" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#E6A23C" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M3 3v18h18"></path>
                  <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"></path>
                </svg>
                <svg v-else-if="report.childFolderType === -3" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#67C23A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"></polyline>
                </svg>
              </div>
              <div class="report-name">{{ report.childFolderName }}</div>
              <div class="report-type">{{ getReportTypeName(report.childFolderType) }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else class="empty-container">
        <div class="empty-icon">📊</div>
        <div class="empty-text">暂无报表数据</div>
      </div>
    </div>
      </van-pull-refresh>
    </div>
  </template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { getFolderWithReports } from '@/api/report'
import type { FolderVO, ChildVO } from '@/api/report/types'

interface Props {
  projectId: number
}

const props = defineProps<Props>()

const router = useRouter()
const reportData = ref<FolderVO[]>([])
const loading = ref(false)
const error = ref('')
const expandedCategory = ref<string | number | null>(null)
const refreshing = ref(false)
const contentRef = ref<HTMLElement>()
let scrollTimeout: number | null = null

// 加载数据
const loadData = async () => {
  loading.value = true
  error.value = ''
  
  try {
    const response = await getFolderWithReports(100, -1)
    // 过滤出第一层分组（childFolderType=11）
    reportData.value = response.data.filter(item => item.childFolderType === 11)
  } catch (err: any) {
    error.value = err.message || '加载数据失败'
    console.error('加载报表数据失败:', err)
  } finally {
    loading.value = false
  }
}

// 切换分类展开状态
const toggleCategory = (categoryId: string | number) => {
  if (expandedCategory.value === categoryId) {
    expandedCategory.value = null
  } else {
    expandedCategory.value = categoryId
  }
}

// 获取分类颜色
const getCategoryColor = (categoryId: string | number) => {
  const colors = [
    'rgba(64, 158, 255, 0.1)',   // 蓝色
    'rgba(230, 162, 60, 0.1)',   // 橙色
    'rgba(103, 194, 58, 0.1)',   // 绿色
    'rgba(245, 108, 108, 0.1)',  // 红色
    'rgba(144, 147, 153, 0.1)',  // 灰色
  ]
  const index = Math.abs(Number(categoryId)) % colors.length
  return colors[index]
}

// 获取分类描述
const getCategoryDescription = (categoryId: string | number) => {
  const descriptions: Record<string, string> = {
    '1': '服务请求和故障处理统计',
    '2': '问题解决和根本原因分析',
    '3': '变更执行和风险评估',
    '4': '配置管理和资产统计',
    '5': '知识管理和文档统计',
  }
  return descriptions[categoryId.toString()] || '报表统计'
}

// 获取报表数量
const getReportCount = (category: FolderVO) => {
  return category.children?.filter(child => child.childFolderType < 0).length || 0
}

// 从分类中获取报表
const getReportsFromCategory = (category: FolderVO): ChildVO[] => {
  return category.children?.filter(child => child.childFolderType < 0) || []
}

// 获取报表类型名称
const getReportTypeName = (reportType: number) => {
  const typeNames: Record<number, string> = {
    [-1]: '列表报表',
    [-2]: '分布图',
    [-3]: '趋势图',
  }
  return typeNames[reportType] || '未知类型'
}

// 处理报表点击
const handleReportClick = (report: ChildVO) => {
  console.log('点击报表:', report)
  
  // 根据报表类型跳转到不同的报表页面
  switch (report.childFolderType) {
    case -1: // 列表报表
      router.push('/report/list')
      break
    case -2: // 分布图
      router.push('/report/distribution')
      break
    case -3: // 趋势图
      router.push('/report/trend')
      break
    default:
      console.warn('未知的报表类型:', report.childFolderType)
  }
}

// 下拉刷新处理
const onRefresh = async () => {
  try {
    await loadData()
  } finally {
    refreshing.value = false
  }
}

// 处理滚动事件
const handleScroll = () => {
  if (contentRef.value) {
    contentRef.value.classList.add('scrolling')
    
    // 清除之前的定时器
    if (scrollTimeout) {
      clearTimeout(scrollTimeout)
    }
    
    // 设置新的定时器，1秒后隐藏滚动条
    scrollTimeout = setTimeout(() => {
      if (contentRef.value) {
        contentRef.value.classList.remove('scrolling')
      }
    }, 1000)
  }
}

// 组件挂载时加载数据
onMounted(async () => {
  await loadData()
  await nextTick()
  
  // 添加滚动事件监听
  if (contentRef.value) {
    contentRef.value.addEventListener('scroll', handleScroll)
  }
})

// 组件卸载时清理
onUnmounted(() => {
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
  if (contentRef.value) {
    contentRef.value.removeEventListener('scroll', handleScroll)
  }
})
</script>

<style scoped>
.content {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 15px;
  background-color: #f5f7fa;
  /* 默认隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* Webkit浏览器隐藏滚动条 */
.content::-webkit-scrollbar {
  width: 0;
  height: 0;
}

/* 滚动时显示滚动条 */
.content.scrolling {
  scrollbar-width: thin; /* Firefox */
  -ms-overflow-style: auto; /* IE and Edge */
}

.content.scrolling::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.content.scrolling::-webkit-scrollbar-track {
  background: transparent;
}

.content.scrolling::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.content.scrolling::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.category-list {
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  overflow: hidden;
}

.refresh-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 14px;
  color: #666;
}

.refresh-button:hover {
  background-color: #e9ecef;
}

.refresh-button .van-icon {
  margin-right: 5px;
}

.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-text,
.error-text,
.empty-text {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.error-icon,
.empty-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.category-header {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  transition: background-color 0.2s;
  cursor: pointer;
}

.category-header:hover {
  background-color: #f9f9f9;
}

.category-header:active {
  background-color: #f0f0f0;
}

.category-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  border-radius: 8px;
}

.category-info {
  flex: 1;
}

.category-title {
  font-weight: 500;
  font-size: 16px;
  color: #333;
}

.category-desc {
  font-size: 13px;
  color: #999;
  margin-top: 4px;
}

.report-count {
  font-size: 13px;
  color: #666;
}

.expand-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s;
}

.category-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
  background-color: #f9f9f9;
}

.category-item.expanded .category-content {
  max-height: 500px;
}

.category-item.expanded .expand-icon {
  transform: rotate(180deg);
}

.report-item {
  display: flex;
  align-items: center;
  padding: 12px 15px 12px 55px;
  border-bottom: 1px solid #eee;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  transition: background-color 0.2s;
}

.report-item:last-child {
  border-bottom: none;
}

.report-item:hover {
  background-color: #f0f0f0;
}

.report-icon {
  margin-right: 10px;
  display: flex;
  align-items: center;
}

.report-name {
  flex: 1;
  color: #333;
}

.report-type {
  font-size: 12px;
  color: #999;
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}
</style>