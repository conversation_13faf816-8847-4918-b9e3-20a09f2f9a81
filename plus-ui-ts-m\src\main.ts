import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// 引入Vant样式
import 'vant/lib/index.css'
// 引入自定义样式
import './styles/index.scss'

// 在桌面端使用触摸模拟器
import '@vant/touch-emulator'

// 注册插件
import plugins from './plugins/index'; 

// 国际化
import i18n from '@/lang/index';

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(plugins);
app.use(i18n);
app.mount('#app') 