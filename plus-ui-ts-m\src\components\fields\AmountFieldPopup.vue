<template>
  <van-popup
    v-model:show="show"
    position="bottom"
    :style="{ height: '40%', width: '100%' }"
    safe-area-inset-bottom
    round
    @click-overlay="handleCancel">
    <van-nav-bar
      :title="field?.fieldName || '金额'"
      left-text="取消"
      right-text="确认"
      @click-left="handleCancel"
      @click-right="handleConfirm"
    />
    <div class="popup-content">
      <van-field 
        v-model="localValue" 
        type="number" 
        :placeholder="'请输入' + (field?.fieldName || '金额')" 
        input-align="right"
        class="amount-field"
      />
      <div class="keyboard-container">
        <van-number-keyboard 
          :show="show"
          @update:show="val => show = val"
          @input="onInput" 
          @delete="onDelete" 
          @close="handleConfirm"
          @blur="onBlur"
          :extra-key="['.']"
          extra-key-only
          theme="custom"
          :decimal-length="2"
          :maxlength="10"
          close-button-text="完成"
        />
      </div>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import type { PageFieldVo } from '@/api/field/types'

defineOptions({
  name: 'AmountFieldPopup'
})

interface Props {
  show: boolean
  field: PageFieldVo | null
  modelValue: string
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'update:modelValue', value: string): void
  (e: 'confirm', value: string | number): void
  (e: 'cancel'): void
  (e: 'input', value: string): void
  (e: 'delete'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const localValue = ref(props.modelValue)

const show = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

watch(() => props.modelValue, (newValue) => {
  localValue.value = newValue
})

const handleConfirm = () => {
  console.log('handleConfirm')
  emit('update:modelValue', localValue.value)
  emit('confirm', localValue.value)
}

const handleCancel = () => {
  localValue.value = props.modelValue
  emit('cancel')
}

const onInput = (value: string | number) => {
  const inputStr = String(value);
  const currentValue = localValue.value;
  
  // 构建新值
  let newValue = currentValue + inputStr;
  
  // 验证格式：只允许数字、小数点，最多2位小数
  if (!/^\d*\.?\d{0,2}$/.test(newValue)) {
    return;
  }
  
  // 确保整数部分不超过10位
  const parts = newValue.split('.');
  if (parts[0].length > 10) {
    return;
  }
  
  localValue.value = newValue;
  emit('input', localValue.value);
}

const onDelete = () => {
  if (localValue.value.length > 0) {
    localValue.value = localValue.value.slice(0, -1);
  }
  emit('delete');
}

const onBlur = () => {
  // 清理以小数点结尾的值
  if (localValue.value.endsWith('.')) {
    localValue.value = localValue.value.slice(0, -1);
  }
}
</script>

<style scoped>
.popup-content {
  padding: 10px, 16px;
  height: calc(100% - 56px);
  display: flex;
  flex-direction: column;
}

.amount-field {
  margin-bottom: 8px; /* 减小底部边距 */
  flex-shrink: 0;
  padding: 8px 12px; /* 添加适当的内边距 */
  border-radius: 4px; /* 添加圆角 */
  background-color: #f8f8f8; /* 添加背景色 */
}

.amount-field :deep(.van-field__control) {
  text-align: right;
  font-size: 18px; /* 减小字体大小 */
  font-weight: 500;
  color: #333; /* 设置字体颜色 */
  line-height: 1.4; /* 设置行高 */
}

.keyboard-container {
  flex: 1; /* 占据剩余空间 */
  overflow: hidden; /* 防止键盘溢出 */
}

/* 确保键盘不遮挡输入框 */
:deep(.van-number-keyboard) {
  position: relative !important;
}
</style>