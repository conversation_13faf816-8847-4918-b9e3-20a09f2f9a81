<template>
  <div class="page-container">
    <!-- 顶部栏 -->
    <van-nav-bar title="工单列表" fixed left-arrow @click-left="$router.back()"/>

    <!-- 搜索框 -->
    <div class="search-container">
      <van-search 
        v-model="search" 
        placeholder="搜索工单..." 
        class="search-input"
      />
      <van-button 
        icon="filter-o" 
        class="filter-btn" 
        @click="showFilterPopup = true"
      />
    </div>

    <!-- 工单列表 -->
    <div class="list-container" ref="listContainerRef">
      <van-list :finished="true" finished-text="">
        <van-cell v-for="item in filteredList" :key="item.id" :to="`/detail/${item.id}`" class="list-item">
          <template #title>
            <div class="list-item-header">
              <span class="list-item-title">{{ item.title }}</span>
                             <div class="list-item-user">
                
                <img :src="item.avatar" class="avatar-img" />
                <span style="margin-left: 5px;">{{ item.user }}</span>
                
              </div>
              
            </div>
          </template>
          <template #label>
            <div class="list-item-no">工单号: {{ item.no }}</div>
            <div class="list-item-desc">{{ item.desc }}</div>
            <div class="list-item-footer">
              <span>{{ item.time }}</span>


              <van-tag 
                :color="convertToBackgroundColor(item.statusColor)" 
                :style="{ color: item.statusColor }"
                class="status-tag">
                {{ item.status }}
              </van-tag>

            </div>
          </template>
        </van-cell>
      </van-list>
    </div>

    <!-- 悬浮新建按钮 -->
    <van-button type="primary" icon="plus" class="fab-btn" @click="$router.push('/new')"></van-button>

    <!-- 底部导航栏 -->
    <van-tabbar route fixed>
      <van-tabbar-item to="/home" icon="home-o">首页</van-tabbar-item>
      <van-tabbar-item to="/modules" icon="apps-o">模块</van-tabbar-item>
      <van-tabbar-item to="/reports" icon="chart-trending-o">报表</van-tabbar-item>
      <van-tabbar-item to="/profile" icon="user-o">我的</van-tabbar-item>
    </van-tabbar>
  </div>

  <!-- 过滤弹出层 -->
  <van-popup 
    v-model:show="showFilterPopup" 
    round
    position="bottom" 
    :style="{ height: '25%' }"
    class="filter-popup"
  >
    <div class="filter-header">
      <div class="filter-title">筛选条件</div>
      <van-button type="primary" size="small" @click="applyFilter">确定</van-button>
    </div>
    
    <div class="filter-content">
      <van-cell-group>
        <van-cell title="状态" is-link @click="openFieldSelector('status')">
          <template #value>
            <span class="filter-value">
              {{ selectedStatuses.length > 0 ? selectedStatuses.join(', ') : '请选择' }}
            </span>
          </template>
        </van-cell>
        <van-cell title="人员" is-link @click="openFieldSelector('user')">
          <template #value>
            <span class="filter-value">
              {{ selectedUsers.length > 0 ? selectedUsers.join(', ') : '请选择' }}
            </span>
          </template>
        </van-cell>
      </van-cell-group>
    </div>
  </van-popup>

  <!-- 字段选择弹出层 -->
  <van-popup 
    v-model:show="showFieldSelector" 
    round
    position="bottom" 
    :style="{ height: fieldSelectorHeight }"
    class="field-selector-popup"
  >
    <div class="field-selector-header">
      <div class="field-selector-title">选择{{ currentField === 'status' ? '状态' : '人员' }}</div>
      <van-button type="primary" size="small" @click="confirmFieldSelection">确定</van-button>
    </div>
    
    <div class="field-selector-content">
      <van-checkbox-group 
        v-model="tempSelectedValues" 
        class="field-selector-checkbox-group"
        v-if="currentField === 'status'"
      >
        <van-checkbox 
          v-for="status in allStatuses" 
          :key="status" 
          :name="status"
          class="field-selector-checkbox"
        >
          {{ status }}
        </van-checkbox>
      </van-checkbox-group>
      
      <van-checkbox-group 
        v-model="tempSelectedValues" 
        class="field-selector-checkbox-group"
        v-if="currentField === 'user'"
      >
        <van-checkbox 
          v-for="user in allUsers" 
          :key="user" 
          :name="user"
          class="field-selector-checkbox"
        >
          {{ user }}
        </van-checkbox>
      </van-checkbox-group>
    </div>
  </van-popup>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { Popup, CheckboxGroup, Checkbox, Icon, Button, Cell, CellGroup } from 'vant'

const search = ref('')
const showFilterPopup = ref(false)
const showFieldSelector = ref(false)
const currentField = ref('')
const selectedStatuses = ref<string[]>([])
const selectedUsers = ref<string[]>([])
const tempSelectedValues = ref<string[]>([])
const list = ref([
  {
    id: 1,
    title: '网络连接问题',
    no: 'WO-2023042501',
    status: '处理中',
    statusColor: '#E6A23C',
    desc: '无法连接到公司内网，已经重启路由器但问题依然存在',
    time: '2023-04-25 14:30',
    user: '张三',
    avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80'
  },
  {
    id: 2,
    title: '账户权限申请',
    no: 'WO-2023042502',
    status: '待审批',
    statusColor: '#409EFF',
    desc: '申请财务系统的只读权限，用于查看部门预算执行情况',
    time: '2023-04-25 10:15',
    user: '李四',
    avatar: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80'
  },
  {
    id: 3,
    title: '软件安装请求',
    no: 'WO-2023042503',
    status: '已完成',
    statusColor: '#67C23A',
    desc: '请求在办公电脑上安装 Adobe Photoshop 软件，用于设计工作',
    time: '2023-04-24 16:20',
    user: '王五',
    avatar: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80'
  },
  {
    id: 4,
    title: 'VPN连接配置',
    no: 'WO-2023042505',
    status: '已完成',
    statusColor: '#67C23A',
    desc: '请求设置远程办公VPN连接，以便在家办公时能够访问公司内部系统',
    time: '2023-04-23 11:30',
    user: '孙七',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80'
  },
  {
    id: 5,
    title: 'VPN连接配置',
    no: 'WO-2023042505',
    status: '已完成',
    statusColor: '#67C23A',
    desc: '请求设置远程办公VPN连接，以便在家办公时能够访问公司内部系统',
    time: '2023-04-23 11:30',
    user: '孙七',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80'
  },
  {
    id: 6,
    title: 'VPN连接配置',
    no: 'WO-2023042505',
    status: '已完成',
    statusColor: '#67C23A',
    desc: '请求设置远程办公VPN连接，以便在家办公时能够访问公司内部系统',
    time: '2023-04-23 11:30',
    user: '孙七',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80'
  },
  {
    id: 7,
    title: 'VPN连接配置',
    no: 'WO-2023042505',
    status: '已完成',
    statusColor: '#67C23A',
    desc: '请求设置远程办公VPN连接，以便在家办公时能够访问公司内部系统',
    time: '2023-04-23 11:30',
    user: '孙七',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=30&h=30&q=80'
  }
])

// 获取所有状态和人员
const allStatuses = computed(() => {
  return [...new Set(list.value.map(item => item.status))]
})

const allUsers = computed(() => {
  return [...new Set(list.value.map(item => item.user))]
})

// 应用过滤条件后的列表
const filteredList = computed(() => {
  let result = list.value
  
  // 应用搜索过滤
  if (search.value) {
    result = result.filter(item =>
      item.title.includes(search.value) ||
      item.no.includes(search.value) ||
      item.user.includes(search.value)
    )
  }
  
  // 应用状态过滤
  if (selectedStatuses.value.length > 0) {
    result = result.filter(item => selectedStatuses.value.includes(item.status))
  }
  
  // 应用人员过滤
  if (selectedUsers.value.length > 0) {
    result = result.filter(item => selectedUsers.value.includes(item.user))
  }
  
  return result
})

// 打开字段选择器
const openFieldSelector = (field: string) => {
  currentField.value = field
  // 根据字段类型设置临时选中值
  if (field === 'status') {
    tempSelectedValues.value = [...selectedStatuses.value]
  } else if (field === 'user') {
    tempSelectedValues.value = [...selectedUsers.value]
  }
  showFieldSelector.value = true
}

// 计算字段选择器高度
const fieldSelectorHeight = computed(() => {
  // 基础高度包括标题区域和一些padding
  const baseHeight = 120; 
  // 每个选项的高度
  const itemHeight = 50;
  
  // 根据当前字段类型获取选项数量
  let itemCount = 0;
  if (currentField.value === 'status') {
    itemCount = allStatuses.value.length;
  } else if (currentField.value === 'user') {
    itemCount = allUsers.value.length;
  }
  
  // 计算总高度，但设置一个最大值
  const calculatedHeight = baseHeight + (itemCount * itemHeight);
  const maxHeight = 400; // 最大高度400px
  const finalHeight = Math.min(calculatedHeight, maxHeight);
  
  return `${finalHeight}px`;
});

// 确认字段选择
const confirmFieldSelection = () => {
  // 根据当前字段类型更新对应的选中值
  if (currentField.value === 'status') {
    selectedStatuses.value = [...tempSelectedValues.value]
  } else if (currentField.value === 'user') {
    selectedUsers.value = [...tempSelectedValues.value]
  }
  showFieldSelector.value = false
}

// 将颜色转换为更浅的背景色
// 应用过滤条件
const applyFilter = () => {
  showFilterPopup.value = false
}

// 重置过滤条件
const resetFilter = () => {
  selectedStatuses.value = []
  selectedUsers.value = []
}

function convertToBackgroundColor(color: string): string {
  if (!color) return '#f0f0f0'
  
  // 如果是十六进制颜色
  if (color.startsWith('#')) {
    // 创建一个更浅的颜色（添加白色混合）
    return color + '20' // 添加20%透明度
  }
  
  // 如果是RGB颜色
  if (color.startsWith('rgb')) {
    // 提取RGB值并添加透明度
    const rgb = color.match(/\d+/g)
    if (rgb && rgb.length >= 3) {
      return `rgba(${rgb[0]}, ${rgb[1]}, ${rgb[2]}, 0.2)`
    }
  }
  
  // 默认返回浅灰色
  return '#f0f0f0'
}

// 添加滚动相关逻辑
const listContainerRef = ref<HTMLElement | null>(null)
let scrollTimer: number | null = null

const handleScroll = () => {
  if (listContainerRef.value) {
    // 添加滚动状态类
    listContainerRef.value.classList.add('scrolling')
    
    // 清除之前的定时器
    if (scrollTimer) {
      clearTimeout(scrollTimer)
    }
    
    // 设置新的定时器，在停止滚动一段时间后移除滚动状态类
    scrollTimer = window.setTimeout(() => {
      if (listContainerRef.value) {
        listContainerRef.value.classList.remove('scrolling')
      }
      scrollTimer = null
    }, 1000) // 1秒后隐藏滚动条
  }
}

onMounted(() => {
  if (listContainerRef.value) {
    listContainerRef.value.addEventListener('scroll', handleScroll)
  }
})

onBeforeUnmount(() => {
  if (listContainerRef.value) {
    listContainerRef.value.removeEventListener('scroll', handleScroll)
  }
  if (scrollTimer) {
    clearTimeout(scrollTimer)
  }
})

</script>

<style scoped>
.page-container {
  position: relative;
  min-height: 100vh;
  padding-top: 46px;
  padding-bottom: 50px;
  box-sizing: border-box;
}

.search-container {
  display: flex;
  align-items: center;
  padding: 0 10px;
  background-color: #f5f5f5;
  height: 50px;
  margin: 12px 0;
}

.search-input {
  flex: 1;
  
}

.filter-btn {
  /* margin-left: 10px; */
  width: 40px;
  height: 50px;
  /* padding: 8px; */
  border: none;
}

.list-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.list-item-title {
  font-weight: bold;
  font-size: 15px;
}

.status-tag {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
}

.list-item-no {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 8px;
}

.list-item-desc {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 12px;
}

.list-item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #9ca3af;
}

.list-item-user {
  display: flex;
  align-items: center;
}

.list-container {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 0 10px 50px 10px;
  max-height: calc(100vh - 46px - 50px - 50px);
  /* 默认隐藏滚动条 */
  scrollbar-width: thin;
  -ms-overflow-style: none;
  
  /* 默认透明滚动条 */
  scrollbar-color: transparent transparent;
}

.list-container.scrolling {
  /* 滚动时显示滚动条 */
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
}

/* Webkit浏览器默认隐藏滚动条 */
.list-container::-webkit-scrollbar {
  width: 8px;
  background: transparent;
}

.list-container::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 4px;
}

/* 滚动时显示滚动条 */
.list-container.scrolling::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
}

.list-item {
  margin-bottom: 8px;
  background-color: #fff;
  border-radius: 8px;
  padding: 12px;
}

.avatar-img {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  margin-left: 4px;
}

.fab-btn {
  position: fixed;
  bottom: 70px;
  right: 16px;
  z-index: 999;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  opacity: 0.9;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
}

/* 过滤弹出层样式 */
.filter-popup {
  display: flex;
  flex-direction: column;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.filter-title {
  font-size: 16px;
  color: #333;
}

.close-icon {
  font-size: 24px;
  color: #999;
}

.filter-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background-color: #f9f9f9;
}

.filter-section {
  margin-bottom: 20px;
  background-color: #fff;
  padding: 10px 15px;
  border-radius: 8px;
}

.filter-section-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #555;
}

.filter-checkbox-group {
  display: flex;
  flex-direction: column;
}

.filter-checkbox {
  margin-bottom: 8px;
}

.filter-footer {
  display: flex;
  justify-content: space-between;
  padding: 15px;
  border-top: 1px solid #f0f0f0;
  background-color: #ffffff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.08);
}

.filter-footer .van-button {
  flex: 1;
  margin: 0 5px;
}

.filter-value {
  color: #999;
}

/* 字段选择弹出层样式 */
.field-selector-popup {
  display: flex;
  flex-direction: column;
}

.field-selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.field-selector-title {
  font-size: 16px;
  color: #333;
}

.field-selector-content {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  background-color: #f9f9f9;
}

.field-selector-checkbox-group {
  display: flex;
  flex-direction: column;
}

.field-selector-checkbox {
  margin-bottom: 8px;
  background-color: #fff;
  padding: 10px 15px;
  border-radius: 8px;
}

.van-search {
  border-radius: 4px;
}
</style>