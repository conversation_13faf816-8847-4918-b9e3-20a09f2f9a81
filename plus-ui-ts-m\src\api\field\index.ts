import request from '@/utils/request';
import type { AxiosResponse } from 'axios';
import { parseStrEmpty } from '@/utils/ruoyi';
import type { PageSettingVo, FieldChoiceBo, ChoiceItemMap } from './types';

// pc端固定客户端授权id
const clientId = import.meta.env.VITE_APP_CLIENT_ID;

/**
 * @param data {LoginData}
 * @returns
 */


export const getProjectPageSetting = (projectId: number, isMobile: number): Promise<AxiosResponse<PageSettingVo>> => {
  return request({
    url: `/servicetrack/projectPage/allPageSetting?projectId=` + parseStrEmpty(projectId) + '&isMobile=' + parseStrEmpty(isMobile),
    method: 'GET'
  });
};


export const listFieldChoices = (fieldChoiceBo: FieldChoiceBo): Promise<AxiosResponse<ChoiceItemMap>> => {
  return request({
    url: '/servicetrack/field/fieldchoiceList',
    method: 'get',
    params: fieldChoiceBo
  });
};