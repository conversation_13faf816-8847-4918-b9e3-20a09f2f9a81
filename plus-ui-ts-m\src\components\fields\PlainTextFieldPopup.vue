<template>
  <FieldPopupWrapper
    v-model:show="show"
    :title="field?.fieldName || '文本编辑'"
    full-height
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <van-field
      v-model="localValue"
      type="textarea"
      :placeholder="`请输入${field.fieldName}`"
      :rows="10"
      autosize
      ref="fieldRef"
      class="field-textarea"
    />
  </FieldPopupWrapper>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import type { PageFieldVo } from '@/api/field/types'
import FieldPopupWrapper from './FieldPopupWrapper.vue'

interface Props {
  show: boolean
  field: PageFieldVo
  modelValue: string
}

const props = defineProps<Props>()

const emit = defineEmits<{ 
  (e: 'update:show', value: boolean): void,
  (e: 'update:modelValue', value: string): void,
  (e: 'confirm', value: string): void,
  (e: 'cancel'): void
}>()

const localValue = ref(props.modelValue)
const fieldRef = ref<any>(null)

const show = defineModel<boolean>('show')

watch(() => props.show, (newVal) => {
  if (newVal) {
    localValue.value = props.modelValue
    // 自动聚焦
    nextTick(() => {
      if (fieldRef.value && fieldRef.value.focus) {
        fieldRef.value.focus()
      }
    })
  }
})

const handleConfirm = () => {
  emit('confirm', localValue.value)
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.field-textarea {
  margin-top: 16px;
  height: 100%;
}

.field-textarea :deep(.van-field__control) {
  height: 100%;
}
</style>