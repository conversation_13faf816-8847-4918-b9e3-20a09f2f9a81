# ITSM移动端Web应用

基于Vue 3.5 + TypeScript + Vant 4的ITSM（IT服务管理）移动端Web应用。

## 技术栈

- **核心框架**: Vue 3.5 + TypeScript + Composition API
- **UI组件库**: Vant 4（国内生态完善，移动端优化）
- **API调用库**: axios
- **适配方案**: postcss-px-to-viewport（纯CSS视口单位，更适合现代浏览器）
- **构建工具**: Vite 5（极速启动 + 按需编译）
- **路由**: Vue Router 4
- **状态管理**: Pinia 3（TypeScript友好，轻量级）
- **工具链**: unplugin-vue-components（自动导入组件） + postcss（CSS处理）
- **测试**: Vitest + @vue/test-utils（组件单元测试）

## 功能特性

### 核心功能
- ✅ 用户登录/退出
- ✅ 工单管理（创建、查看、处理）
- ✅ 服务目录浏览
- ✅ 知识库查询
- ✅ 个人中心

### 移动端优化
- ✅ 响应式设计
- ✅ 触摸友好交互
- ✅ 下拉刷新/上拉加载
- ✅ 移动端适配（375px设计稿）

### 开发体验
- ✅ TypeScript支持
- ✅ 自动导入组件
- ✅ 热更新
- ✅ ESLint代码规范

## 项目结构

```
src/
├── components/          # 公共组件
│   ├── WorkOrderList.vue
│   ├── ServiceList.vue
│   └── KnowledgeList.vue
├── views/              # 页面组件
│   ├── Login.vue
│   ├── Dashboard.vue
│   ├── WorkOrder.vue
│   ├── CreateWorkOrder.vue
│   ├── WorkOrderDetail.vue
│   ├── ServiceCatalog.vue
│   ├── Knowledge.vue
│   └── Profile.vue
├── stores/             # 状态管理
│   ├── index.ts
│   └── user.ts
├── router/             # 路由配置
│   └── index.ts
├── utils/              # 工具函数
│   └── request.ts
├── styles/             # 样式文件
│   └── index.scss
├── App.vue             # 根组件
└── main.ts             # 入口文件
```

## 快速开始

### 安装依赖

```bash
npm install
```

### 开发环境

```bash
npm run dev
```

### 预览（测试生产构建）

```bash
npm run preview:preview
```

### 构建生产版本

```bash
npm run build:prod
```

### 代码检查

```bash
npm run lint
```

### 类型检查

```bash
npm run type-check
```

### 运行测试

```bash
npm run test
```

## 环境变量

创建 `.env` 文件：

```env
VITE_APP_TITLE=ITSM移动端
VITE_API_BASE_URL=/api
```

## 开发指南

### 添加新页面

1. 在 `src/views/` 创建页面组件
2. 在 `src/router/index.ts` 添加路由配置
3. 更新底部导航栏（如需要）

### 添加新组件

1. 在 `src/components/` 创建组件
2. 使用 `unplugin-vue-components` 自动导入

### 样式规范

- 使用SCSS编写样式
- 遵循BEM命名规范
- 使用CSS变量管理主题色
- 移动端适配使用vw单位

### API调用

使用 `src/utils/request.ts` 封装的axios实例：

```typescript
import request from '@/utils/request'

// GET请求
const getData = () => request.get('/api/data')

// POST请求
const createData = (data) => request.post('/api/data', data)
```

## 部署说明

### 构建

```bash
npm run build
```

构建产物位于 `dist/` 目录。

### 部署到服务器

将 `dist/` 目录内容部署到Web服务器即可。

### 移动端优化

- 已配置移动端视口
- 支持PWA（可进一步配置）
- 触摸事件优化

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交Issue。 