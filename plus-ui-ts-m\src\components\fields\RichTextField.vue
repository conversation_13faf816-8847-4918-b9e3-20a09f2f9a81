<template>
  <div 
    class="normal-value editable-field-container field-value-left consistent-height"
    @click="editField"
  >
    <span v-if="displayValue" class="normal-value" v-html="displayValue"></span>
    <span v-else class="empty-value">空</span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { PageFieldVo } from '@/api/field/types'

interface Props {
  field: PageFieldVo
  modelValue: string
}

const props = defineProps<Props>()

const emit = defineEmits<{ 
  (e: 'edit', field: PageFieldVo, event: Event): void
}>()

// base64解码函数
const decodeBase64 = (base64String: string): string => {
  try {
    // 检查是否为有效的base64字符串
    if (!base64String || typeof base64String !== 'string') {
      return base64String
    }
    
    // 尝试解码base64
    return atob(base64String)
  } catch (error) {
    console.warn('Base64解码失败:', error)
    // 如果解码失败，返回原始字符串
    return base64String
  }
}

const displayValue = computed(() => {
  // 对富文本字段进行base64解码
  return decodeBase64(props.modelValue) || ''
})

const editField = (event: Event) => {
  emit('edit', props.field, event)
}
</script>

<style scoped>
.editable-field-container {
  width: 100%;
  padding: 6px 0;
  line-height: 1.5;
  box-sizing: border-box;
  text-align: left;
}

.normal-value {
  color: #333;
  font-size: 14px;
  text-align: left;
}

.empty-value {
  color: #ccc;
  font-size: 14px;
  text-align: left;
}

.consistent-height {
  min-height: 32px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
}
</style>