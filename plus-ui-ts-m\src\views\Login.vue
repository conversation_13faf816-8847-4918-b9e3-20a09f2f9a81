<template>
  <div class="login-page">
    <div class="login-container">
      <div class="login-header">
        <div class="logo">
          <van-icon name="setting-o" size="48" color="#1989fa" />
        </div>
        <h1 class="title">ITSM手机端</h1>
        <p class="subtitle">IT服务管理平台</p>
      </div>

      <van-form @submit="onSubmit" class="login-form">
        <van-cell-group inset>
          <van-field
            v-model="loginForm.username"
            name="username"
            label="用户名"
            placeholder="请输入用户名"
            :rules="[{ required: true, message: '请输入用户名' }]"
            left-icon="user-o"
          />
          <van-field
            v-model="loginForm.password"
            type="password"
            name="password"
            label="密码"
            placeholder="请输入密码"
            :rules="[{ required: true, message: '请输入密码' }]"
            left-icon="lock"
          />
        </van-cell-group>

        <div class="login-options">
          <van-checkbox v-model="loginForm.rememberMe">记住密码</van-checkbox>
          <!-- <van-button type="primary" size="small" plain>忘记密码？</van-button> -->
        </div>

        <div class="login-button">
          <van-button
            round
            block
            type="primary"
            native-type="submit"
            :loading="loading"
            size="large"
          >
            登录
          </van-button>
        </div>
      </van-form>

      <!-- <div class="login-footer">
        <p>演示账号：admin / adm123</p>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import type { LoginData } from '@/api/types';
import { showToast, showNotify } from 'vant'

const router = useRouter()
const userStore = useUserStore()

const loading = ref(false)
const loginForm = ref<LoginData>({
  tenantId: '000000',
  username: '',
  password: '',
  rememberMe: false,
  code: '',
  uuid: '##&&##' //ignore captcha temporiraly
} as LoginData);

const onSubmit = async (values: any) => {
  loading.value = true
  
    // 勾选了需要记住密码设置在 localStorage 中设置记住用户名和密码
    if (loginForm.value.rememberMe) {
        localStorage.setItem('tenantId', String(loginForm.value.tenantId));
        localStorage.setItem('username', String(loginForm.value.username));
        localStorage.setItem('password', String(loginForm.value.password));
        localStorage.setItem('rememberMe', String(loginForm.value.rememberMe));
      } else {
        // 否则移除
        localStorage.removeItem('tenantId');
        localStorage.removeItem('username');
        localStorage.removeItem('password');
        localStorage.removeItem('rememberMe');
      }
      // 调用action的登录方法
    try {
      await userStore.login(loginForm.value)
      showToast({
        message: '登录成功',
        type: 'success'
      })
       // 获取项目列表--to do
      //  const {data} = await getProjectListByUser();
      //   if (data && data.length > 0) {
      //     // 如果项目列表长度大于1，则设置基础项目 -- to do  need to get base project id by user setting once user login to select one base project
      //     if (data.length > 1) {

      //     }

      //     userStore.setProjectList(data, 0);
      //   }
      //   else{
      //     if(userStore.stUserType == eSTUserType.ST_SWP || userStore.stUserType == eSTUserType.ST_EWP_SWP){
      //       loading.value = false;
      //       // 重新获取验证码
      //       showToast({
      //         message: '您未参加任何工作流程.',
      //         type: 'fail'
      //       })
      //       return;
      //     }
      //   }
      // 跳转到首页
      router.push('/home')
    } catch (error) {
      console.error(error)     
    } finally {
      loading.value = false
    }
}
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-container {
  width: 100%;
  max-width: 400px;
  background: #fff;
  border-radius: 16px;
  padding: 40px 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;

  .logo {
    margin-bottom: 16px;
  }

  .title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
  }

  .subtitle {
    font-size: 14px;
    color: var(--text-color-2);
  }
}

.login-form {
  margin-bottom: 24px;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 16px 0 24px;
  padding: 0 16px;
}

.login-button {
  margin-bottom: 24px;
}

.login-footer {
  text-align: center;
  
  p {
    font-size: 12px;
    color: var(--text-color-3);
    margin: 0;
  }
}

// 强制覆盖 Notify 样式 - 直接在组件中定义
:deep(.van-notify.custom-notify) {
  background: #fff0f0 !important;
  border: 1px solid #f56c6c !important;
  color: #f56c6c !important;
  border-radius: 6px !important;
  box-shadow: none !important;
  min-width: 180px !important;
  max-width: 90vw !important;
  margin: 24px auto !important;
  text-align: center !important;
  font-size: 15px !important;
  padding: 8px 20px 8px 36px !important;
  position: relative !important;

  .van-notify__text {
    color: #f56c6c !important;
    font-size: 15px !important;
    padding: 0 !important;
    line-height: 1.6 !important;
    text-align: center !important;
  }

  // 左侧圆形感叹号
  &::before {
    content: '!' !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: absolute !important;
    left: 12px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    width: 18px !important;
    height: 18px !important;
    background: #f56c6c !important;
    color: #fff !important;
    border-radius: 50% !important;
    font-size: 14px !important;
    font-weight: bold !important;
    box-shadow: 0 0 2px #f56c6c44 !important;
  }
}
</style> 